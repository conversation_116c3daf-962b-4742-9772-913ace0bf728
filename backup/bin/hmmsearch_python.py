#!/usr/bin/env python3
"""
Python script to run hmmsearch in parallel on a working node.

This script is equivalent to hmmsearch_par_optimized_2.sh but:
1) Runs directly on the working node instead of being submitted to a cluster
2) Takes a single batch directory as input
3) Takes the database path as input

Usage:
    python hmmsearch_python.py hmm_directory database_file output_directory [--num_processes 15]

Example:
    python hmmsearch_python.py /path/to/hmm_files test_genome.faa hit_1
"""

import os
import sys
import argparse
import subprocess
import glob
import multiprocessing
from concurrent.futures import ProcessPoolExecutor, as_completed


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Run hmmsearch in parallel on a working node.')

    parser.add_argument('hmm_dir',
                        help='Directory containing HMM files')

    parser.add_argument('db_path',
                        help='Path to the database file (.faa)')

    parser.add_argument('output_dir',
                        help='Directory to store output files')

    parser.add_argument('--num_processes', type=int, default=15,
                        help='Number of parallel processes to run (default: %(default)s)')

    return parser.parse_args()


def run_hmmsearch(hmm_file, hits_dir, db_path):
    """
    Run hmmsearch for a single HMM file.

    Args:
        hmm_file: Path to the HMM file
        hits_dir: Directory to store the output files
        db_path: Path to the database file

    Returns:
        Tuple of (hmm_file, success, message)
    """
    try:
        # Extract the filename
        hmm_filename = os.path.basename(hmm_file)

        # Extract the database name (without extension)
        db_filename = os.path.basename(db_path)
        db_name = os.path.splitext(db_filename)[0]

        # Create the output filename: <database_name>_<input_hmm_file_name>.tbl
        output_filename = f"{db_name}_{hmm_filename.replace('.hmm', '.tbl')}"
        hits_file = os.path.join(hits_dir, output_filename)

        # Check if the output file already exists
        if os.path.exists(hits_file):
            return hmm_file, True, f"File {hits_file} already exists. Skipping."

        # Run hmmsearch
        cmd = [
            "hmmsearch",
            "--tblout", hits_file,
            "-E", "1000",
            "--incE", "1000",
            "-Z", "20417281",
            hmm_file,
            db_path
        ]

        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode != 0:
            return hmm_file, False, f"Error running hmmsearch: {result.stderr}"

        return hmm_file, True, f"Successfully ran hmmsearch for {hmm_file}"

    except Exception as e:
        return hmm_file, False, f"Exception running hmmsearch: {str(e)}"


def main():
    """Main function to run hmmsearch in parallel."""
    # Parse command line arguments
    args = parse_arguments()

    # Set up paths
    hmms_dir = args.hmm_dir
    hits_dir = args.output_dir
    db_path = args.db_path

    # Check if paths exist
    if not os.path.exists(hmms_dir):
        print(f"Error: HMMs directory {hmms_dir} does not exist.")
        sys.exit(1)

    if not os.path.exists(db_path):
        print(f"Error: Database file {db_path} does not exist.")
        sys.exit(1)

    # Create the output directory if it doesn't exist
    os.makedirs(hits_dir, exist_ok=True)

    # Find all HMM files
    hmm_files = glob.glob(os.path.join(hmms_dir, "trimmed_Orthogroup*.hmm"))

    if not hmm_files:
        print(f"Error: No HMM files found in {hmms_dir}")
        sys.exit(1)

    print(f"Found {len(hmm_files)} HMM files in {hmms_dir}")

    # Determine the number of processes to use
    num_processes = min(args.num_processes, multiprocessing.cpu_count())
    print(f"Using {num_processes} processes for parallel execution")

    # Run hmmsearch in parallel
    with ProcessPoolExecutor(max_workers=num_processes) as executor:
        futures = {
            executor.submit(run_hmmsearch, hmm_file, hits_dir, db_path): hmm_file
            for hmm_file in hmm_files
        }

        # Process results as they complete
        for i, future in enumerate(as_completed(futures), 1):
            # We don't need to use the file_path here
            _, _, message = future.result()

            # Print progress
            print(f"[{i}/{len(hmm_files)}] {message}")

    print("All hmmsearch tasks completed!")


if __name__ == "__main__":
    main()
