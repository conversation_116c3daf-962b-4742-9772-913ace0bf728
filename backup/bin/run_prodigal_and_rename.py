#!/usr/bin/env python3
# Script to run Prodigal for protein annotation and rename headers
"""
This script executes Prodigal for protein annotation on an input FNA file and then:
1. Renames the headers in the output FAA file to follow the format: ><species>_<protein_id>
2. <PERSON><PERSON><PERSON> stop codons (asterisks '*') from the protein sequences

Requirements:
- Prodigal must be installed and available in your PATH or conda environment
- Python 3.6 or higher

Usage:
    1. Activate the conda environment with Prodigal:
       conda activate prodigal

    2. Run the script with basic options:
       python run_prodigal_and_rename.py input.fna

    3. Specify an output file location:
       python run_prodigal_and_rename.py input.fna -o /path/to/output.faa

    4. Generate a GFF file with annotations:
       python run_prodigal_and_rename.py input.fna -g output.gff

    5. Use metagenomic mode for metagenomes:
       python run_prodigal_and_rename.py input.fna -p meta

    6. Combine multiple options:
       python run_prodigal_and_rename.py input.fna -o output.faa -g output.gff -p meta

Notes:
    - The species name used in the header is derived from the output filename (without extension)
    - Make sure the directory for the output file exists before running the script
    - The script will overwrite existing files without warning
"""

import os
import sys
import subprocess
import argparse

def run_prodigal(input_fna, output_faa, output_gff=None, mode="single"):
    """
    Run Prodigal on the input FNA file to generate protein annotations.

    Args:
        input_fna: Path to the input FNA file
        output_faa: Path to the output FAA file for protein sequences
        output_gff: Path to the output GFF file (optional)
        mode: Prodigal mode, either "single" or "meta" (default: "single")

    Returns:
        True if Prodigal ran successfully, False otherwise
    """
    if not os.path.exists(input_fna):
        print(f"Error: Input file {input_fna} does not exist.")
        return False

    # Create the command
    cmd = ["prodigal", "-i", input_fna, "-a", output_faa, "-p", mode]

    # Add GFF output if specified
    if output_gff:
        cmd.extend(["-o", output_gff, "-f", "gff"])

    print(f"Running Prodigal: {' '.join(cmd)}")

    try:
        # Run Prodigal
        subprocess.run(cmd, check=True, capture_output=True, text=True)
        print("Prodigal completed successfully.")
        return True
    except subprocess.CalledProcessError as e:
        print(f"Error running Prodigal: {e}")
        print(f"Prodigal stderr: {e.stderr}")
        return False

def modify_fasta_headers_and_remove_asterisks(fasta_file_path):
    """
    Modify the headers of the FASTA file and remove asterisks (stop codons) from sequences.
    This function is adapted from headers.py with additional functionality to remove stop codons.

    Args:
        fasta_file_path: Path to the FASTA file to modify
    """
    if not os.path.isfile(fasta_file_path):
        print(f"Error: {fasta_file_path} is not a valid file.")
        return

    species_name = os.path.basename(fasta_file_path).replace('.faa', '')
    modified_lines = []
    current_sequence = ""
    current_header = ""

    try:
        with open(fasta_file_path, 'r', errors='ignore') as f:
            for line in f:
                if line.startswith('>'):
                    # Process previous sequence if it exists
                    if current_header and current_sequence:
                        # Remove asterisks from sequence
                        cleaned_sequence = current_sequence.replace('*', '')
                        # Add the header and cleaned sequence to modified_lines
                        modified_lines.append(current_header)
                        modified_lines.append(cleaned_sequence + '\n')

                    # Process new header
                    header_content = line.split()[0][1:]  # Only the first part of the header
                    current_header = f'>{species_name}_{header_content}\n'
                    current_sequence = ""
                else:
                    # Accumulate sequence lines
                    current_sequence += line.strip()

            # Process the last sequence
            if current_header and current_sequence:
                # Remove asterisks from sequence
                cleaned_sequence = current_sequence.replace('*', '')
                # Add the header and cleaned sequence to modified_lines
                modified_lines.append(current_header)
                modified_lines.append(cleaned_sequence + '\n')

        # Overwrite the original file with modified headers and cleaned sequences
        with open(fasta_file_path, 'w') as f:
            f.writelines(modified_lines)

        print(f"Headers modified and stop codons (*) removed in {fasta_file_path}.")

    except Exception as e:
        print(f"Error modifying headers and removing stop codons: {e}")

def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Run Prodigal and rename headers')
    parser.add_argument('input_fna', help='Input FNA file')
    parser.add_argument('-o', '--output_faa', help='Output FAA file (default: input_name.faa)')
    parser.add_argument('-g', '--output_gff', help='Output GFF file (optional)')
    parser.add_argument('-p', '--mode', choices=['single', 'meta'], default='single',
                        help='Prodigal mode (default: single)')

    args = parser.parse_args()

    # Set default output file name if not provided
    if not args.output_faa:
        args.output_faa = os.path.splitext(args.input_fna)[0] + '.faa'

    # Run Prodigal
    success = run_prodigal(args.input_fna, args.output_faa, args.output_gff, args.mode)

    if success:
        # Modify headers in the output FAA file and remove stop codons
        modify_fasta_headers_and_remove_asterisks(args.output_faa)
        print(f"Process completed. Output file: {args.output_faa}")
    else:
        print("Process failed.")
        sys.exit(1)

if __name__ == "__main__":
    main()
