#!/usr/bin/env nextflow

/*
========================================================================================
    Genomic Data Processing Workflow
========================================================================================
    This workflow processes genomic data through a series of steps:
    1. Prodigal for protein annotation
    2. GTDB-Tk for taxonomic classification
    3. HMMER for protein family analysis
    4. Processing of HMMER hits
    5. Creation of a unified dataframe with taxonomic information
========================================================================================
*/

nextflow.enable.dsl = 2

// Print workflow header
log.info """
=======================================================
Genomic Data Processing Workflow
=======================================================
genome       : ${params.genome}
outdir       : ${params.outdir}
gtdbtk_db    : ${params.gtdbtk_db}
hmm_dir      : ${params.hmm_dir}
=======================================================
"""

// Check input parameters
if (!file(params.genome).exists()) {
    exit 1, "Input genome file not found: ${params.genome}"
}

if (!file(params.gtdbtk_db).exists()) {
    exit 1, "GTDB-Tk database directory not found: ${params.gtdbtk_db}"
}

if (!file(params.hmm_dir).exists()) {
    exit 1, "HMM directory not found: ${params.hmm_dir}"
}

// Create output directory
file(params.outdir).mkdirs()

/*
========================================================================================
    PROCESSES
========================================================================================
*/

// Process 1: Run Prodigal for protein annotation
process PRODIGAL {
    publishDir "${params.outdir}/prodigal", mode: 'copy'

    input:
    path genome
    path script

    output:
    path "${genome.baseName}.faa", emit: faa

    script:
    """
    python ${script} ${genome}
    """
}

// Process 2: Run GTDB-Tk for taxonomic classification
process GTDBTK {
    publishDir "${params.outdir}/gtdbtk", mode: 'copy'

    input:
    path genome
    path script

    output:
    path "gtdb_classification.csv", emit: classification

    script:
    """
    # Create a directory for the genome
    mkdir -p genome_dir
    cp ${genome} genome_dir/

    # Run GTDB-Tk
    python ${script} --genome_dir genome_dir --data_path ${params.gtdbtk_db} --clean_outputs --csv_output gtdb_classification.csv
    """
}

// Process 3: Run HMMSEARCH for protein family analysis
process HMMSEARCH {
    publishDir "${params.outdir}/hmmsearch", mode: 'copy', saveAs: { filename -> filename.replaceAll("^hmmsearch/", "") }

    input:
    path faa
    path hmm_dir
    path script

    output:
    path "hmmsearch/${hmm_dir.name}", emit: hits_dir

    script:
    """
    # Create output directory with batch name
    mkdir -p hmmsearch/${hmm_dir.name}

    # Run HMMSEARCH
    python ${script} ${hmm_dir} ${faa} hmmsearch/${hmm_dir.name} --num_processes ${task.cpus}
    """
}

// Process 4: Process HMMSEARCH hits
process PROCESS_HITS {
    publishDir "${params.outdir}/processed_hits", mode: 'copy', saveAs: { filename -> filename.replaceAll("^processed_hits/", "") }

    input:
    path hits_dir
    path script

    output:
    path "processed_hits/${hits_dir.name}", emit: csv_hits

    script:
    // Extract the batch name from the hits_dir path
    def batch_name = hits_dir.name
    """
    # Create output directory with batch name
    mkdir -p processed_hits/${batch_name}

    # Process hits - use the directory directly without adding batch_name again
    python ${script} ${hits_dir} processed_hits/${batch_name}
    """
}

// Process 5: Create unified dataframe
process CREATE_DATAFRAME {
    publishDir "${params.outdir}", mode: 'copy'

    input:
    path csv_hits
    path classification
    path script

    output:
    path "final_results*.csv", emit: final_results

    script:
    // Extract the batch name for the output file
    def batch_name = csv_hits.name.split('/')[-1]
    """
    # Create unified dataframe with batch name
    python ${script} ${csv_hits} ${classification} final_results_${batch_name}.csv

    # Create a copy with the standard name for backward compatibility
    cp final_results_${batch_name}.csv final_results.csv
    """
}

/*
========================================================================================
    WORKFLOW
========================================================================================
*/

// Main workflow
workflow {
    // Define channels
    genome_ch = Channel.fromPath(params.genome)
    hmm_dir_ch = Channel.fromPath(params.hmm_dir)

    // Define script channels
    prodigal_script_ch = Channel.fromPath("$baseDir/bin/run_prodigal_and_rename.py")
    gtdbtk_script_ch = Channel.fromPath("$baseDir/bin/run_gtdbtk.py")
    hmmsearch_script_ch = Channel.fromPath("$baseDir/bin/hmmsearch_python.py")
    convert_hits_script_ch = Channel.fromPath("$baseDir/bin/convert_hits.py")
    create_df_script_ch = Channel.fromPath("$baseDir/bin/create_unique_df_hits_optimized.py")

    // Run processes
    PRODIGAL(genome_ch, prodigal_script_ch)
    GTDBTK(genome_ch, gtdbtk_script_ch)
    HMMSEARCH(PRODIGAL.out.faa, hmm_dir_ch, hmmsearch_script_ch)
    PROCESS_HITS(HMMSEARCH.out.hits_dir, convert_hits_script_ch)
    CREATE_DATAFRAME(PROCESS_HITS.out.csv_hits, GTDBTK.out.classification, create_df_script_ch)
}
