# Genomic Data Processing Workflow

This Nextflow workflow processes genomic data through a series of steps:
1. <PERSON><PERSON><PERSON> for protein annotation
2. GTDB-Tk for taxonomic classification
3. HMMER for protein family analysis (in parallel for multiple batch directories)
4. Processing of HMMER hits (in parallel for multiple batch directories)
5. Creation of a unified dataframe with taxonomic information

## Requirements

- Nextflow (version 20.10.0 or later)
- Singularity (version 3.0 or later)
- Sufficient disk space for the GTDB-Tk database and intermediate files

## Setup

1. Clone this repository:
   ```
   git clone <repository-url>
   cd <repository-directory>
   ```

2. Build the Singularity containers:
   ```
   bash build_containers.sh
   ```
   This will create the following containers in the `containers` directory:
   - `prodigal.sif`: Prodigal with Python
   - `gtdbtk.sif`: GTDB-Tk with Python
   - `hmmer.sif`: HMMER with Python

## Directory Structure

The workflow expects the following directory structure for the HMM profiles:

```
hmm_base_dir/
├── batch_1/
│   ├── hmm_file1.hmm
│   ├── hmm_file2.hmm
│   └── ...
├── batch_2/
│   ├── hmm_file1.hmm
│   ├── hmm_file2.hmm
│   └── ...
├── batch_3/
│   └── ...
└── batch_4/
    └── ...
```

## Configuration

The pipeline is configured in the `nextflow.config` file. You can modify the following parameters:

- `params.genome`: Path to the input genome file (default: `GCF_008369605.1.fna`)
- `params.outdir`: Directory for output files (default: `results`)
- `params.gtdbtk_db`: Path to the GTDB-Tk database (default: `/clusterfs/jgi/scratch/science/mgs/nelli/databases/gtdbtk/release220`)
- `params.hmm_base_dir`: Path to the base directory containing batch directories with HMM files (default: `/clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/1_pangenomes_features/genera_pangenome_est_30_Sept/hmm_est_5_30_Sept`)
- `params.max_cpus`: Maximum number of CPUs to use (default: 16)
- `params.max_memory`: Maximum memory to use (default: 32 GB)

## Running the Pipeline

To run the pipeline with default parameters:

```
nextflow run main.nf
```

To specify a different input genome:

```
nextflow run main.nf --genome /path/to/your/genome.fna
```

To specify a different output directory:

```
nextflow run main.nf --outdir /path/to/output
```

To run with custom HMM base directory and GTDB-Tk database:

```
nextflow run main.nf --hmm_base_dir /path/to/hmm_directories --gtdbtk_db /path/to/gtdbtk_db
```

## Output

The pipeline generates the following outputs:

- `results/prodigal/`: Prodigal protein annotations
- `results/gtdbtk/`: GTDB-Tk taxonomic classification
- `results/hmmsearch/`: HMMER search results for each batch directory
- `results/processed_hits/`: Processed HMMER hits for each batch directory
- `results/processed_hits_combined/`: Combined processed hits from all batch directories
- `results/final_results.csv`: Final unified dataframe with taxonomic information and protein family hits

## Pipeline Components

### 1. Prodigal

Runs Prodigal to annotate proteins in the input genome and renames the headers. This process is executed only once.

### 2. GTDB-Tk

Runs GTDB-Tk to classify the genome taxonomically. This process is executed only once.

### 3. HMMSEARCH

Runs HMMER to search for protein families in the annotated proteins. This process is executed in parallel for each batch directory.

### 4. Process Hits

Processes the HMMER hits to convert them to CSV format. This process is executed in parallel for each batch directory.

### 5. Create Dataframe

Creates a unified dataframe with taxonomic information and protein family hits from all batch directories.

## Workflow Execution

The workflow executes as follows:

1. PRODIGAL and GTDBTK are executed once to process the input genome
2. For each batch directory (batch_1, batch_2, batch_3, batch_4):
   - HMMSEARCH is executed in parallel to search for protein families
   - PROCESS_HITS is executed in parallel to convert the hits to CSV format
3. All processed hits are collected and combined
4. CREATE_DATAFRAME creates the final unified dataframe

## Troubleshooting

- If you encounter issues with Singularity, make sure you have sufficient permissions to create and run Singularity containers.
- If GTDB-Tk fails, check that the database path is correct and accessible.
- For memory issues, try reducing the number of CPUs or increasing the memory allocation in the `nextflow.config` file.
- If batch directories are not being processed, check that they follow the expected naming pattern (batch_1, batch_2, etc.) and exist in the specified hmm_base_dir.
- Check the work directory for error logs if any process fails.

## License

This pipeline is licensed under the MIT License. See the LICENSE file for details.
