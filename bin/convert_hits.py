# -- <PERSON><PERSON><PERSON> to convert the hmmsearch .tbl files to .csv and rename them (single batch version) --
# Example usage:
# python convert_hits.py /path/to/input_batch_directory /path/to/output_directory
#
# This script:
# 1) Converts the tbl files in a single batch directory to .csv files
# 2) If a tbl file contains an empty table (only headers), it inserts in "target name" the name of the file
#    before the second underscore and in "query name" the last part of the file name (Orthogroup<number>)
# 3) Renames the resulting CSV files to their 'query_name' values (without the "trimmed_" prefix)
#
# Note: The output_directory should be the exact directory where you want the CSV files to be saved.
# For example, if your input is "hits/batch_1" and you want output in "csv_hits/batch_1",
# then specify "csv_hits/batch_1" as the output directory, not just "csv_hits".

import os
import pandas as pd
from io import StringIO
import sys

def extract_filename_parts(filename):
    """Extract parts from filename for empty tables."""
    # Remove .tbl extension
    base_name = filename.replace('.tbl', '')

    # Split by underscore
    parts = base_name.split('_')

    # Find the position of "trimmed" which separates target and query parts
    trimmed_idx = -1
    for i, part in enumerate(parts):
        if part == "trimmed":
            trimmed_idx = i
            break

    # If "trimmed" is found, use everything before it plus "trimmed" as target_name
    if trimmed_idx > 0:
        target_name = '_'.join(parts[:trimmed_idx+1])
    # Otherwise use everything before the last part
    elif len(parts) > 1:
        target_name = '_'.join(parts[:-1])
    else:
        target_name = parts[0] if parts else "unknown"

    # Get the last part (Orthogroup<number>)
    query_name = parts[-1] if parts else "unknown"

    return target_name, query_name

def process_table(file_path):
    """Process a .tbl file and convert it to a pandas DataFrame."""
    with open(file_path, 'r') as file:
        lines = file.readlines()

    if len(lines) < 3:
        print(f"Skipping {file_path} as it does not have enough lines.")
        return None

    column_names = [
        'target_name', 'accession', 'query_name', 'query_accession',
        'E-value', 'score', 'bias', 'domain_E-value', 'domain_score', 'domain_bias',
        'exp', 'reg', 'clu', 'ov', 'env', 'dom', 'rep', 'inc', 'description_of_target'
    ]

    # Check if table is empty (only contains headers)
    is_empty_table = True
    for line in lines:
        if not line.startswith('#') and line.strip():
            is_empty_table = False
            break

    if is_empty_table:
        print(f"Empty table detected in {file_path}. Creating custom entry.")
        filename = os.path.basename(file_path)
        target_name, query_name = extract_filename_parts(filename)

        # Create a DataFrame with a single row for empty tables
        data = {
            'target_name': [target_name],
            'accession': ['-'],
            'query_name': [query_name],
            'query_accession': ['-'],
            'E-value': [0.0],
            'score': [0.0],
            'bias': [0.0],
            'domain_E-value': [0.0],
            'domain_score': [0.0],
            'domain_bias': [0.0],
            'exp': [0],
            'reg': [0],
            'clu': [0],
            'ov': [0],
            'env': [0],
            'dom': [0],
            'rep': [0],
            'inc': [0],
            'description_of_target': ['-']
        }
        return pd.DataFrame(data)

    # Process non-empty tables as before
    lines = lines[2:]
    lines.pop(0)

    end_idx = None
    for idx, line in enumerate(lines):
        if line.startswith("# Program"):
            end_idx = idx
            break

    if end_idx is None:
        print(f"Warning: Could not find end of table in {file_path}. Using all lines after headers.")
        table_lines = lines
    else:
        table_lines = lines[:end_idx]

    table_data = ''.join(table_lines)

    if not table_data.strip():
        print(f"Warning: No data found in {file_path} after processing.")
        return None

    try:
        df = pd.read_csv(StringIO(table_data), sep=r'\s+', names=column_names, comment='#')
        return df
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return None

def get_new_filename(df, original_filename):
    """Get the new filename based on the query_name in the DataFrame."""
    try:
        # Check if 'query_name' column exists and is not empty
        if 'query_name' not in df.columns or df['query_name'].empty:
            print(f"Warning: 'query_name' column not found or empty in {original_filename}")
            return None

        # Retrieve the "query_name" value
        query_name = df['query_name'].iloc[0]

        # Remove the "trimmed_" substring if present
        new_name = query_name.replace("trimmed_", "")

        return new_name + ".csv"
    except Exception as e:
        print(f"Error getting new filename for {original_filename}: {e}")
        return None

def process_batch_directory(input_batch_dir, output_dir):
    """
    Process a single batch directory.

    Args:
        input_batch_dir: Path to the batch directory to process
        output_dir: Directory where the output files will be created (should already include batch name if needed)
    """
    # Get the batch name from the input directory
    batch_name = os.path.basename(input_batch_dir)
    print(f"Processing batch directory: {batch_name}")

    # Create the output directory if it doesn't exist
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
        print(f"Created output directory: {output_dir}")

    # Use the output directory directly without creating an additional batch directory
    batch_output_dir = output_dir
    os.makedirs(batch_output_dir, exist_ok=True)
    print(f"Using output directory: {batch_output_dir}")

    # Process all tbl files in the batch directory
    files_processed = 0

    # Check if there are any tbl files in the input directory
    tbl_files = [f for f in os.listdir(input_batch_dir) if f.endswith('.tbl')]

    if tbl_files:
        # Process actual tbl files
        for filename in tbl_files:
            file_path = os.path.join(input_batch_dir, filename)
            df = process_table(file_path)

            if df is not None:
                # Get the new filename based on query_name
                new_filename = get_new_filename(df, filename)

                if new_filename:
                    # Save the DataFrame directly to the new CSV file
                    output_file_path = os.path.join(batch_output_dir, new_filename)
                    df.to_csv(output_file_path, index=False)
                    print(f"Processed and saved as {output_file_path}")
                else:
                    # If we couldn't get a new filename, use the original name with .csv extension
                    output_file_path = os.path.join(batch_output_dir, filename.replace('.tbl', '.csv'))
                    df.to_csv(output_file_path, index=False)
                    print(f"Processed and saved as {output_file_path} (using original name)")
                files_processed += 1
    else:
        # Exit with error when no tbl files are found
        print(f"Error: No .tbl files found in {input_batch_dir}")
        print("Please ensure that hmmsearch has been run successfully and generated .tbl files.")
        sys.exit(1)

    print(f"\nProcessing complete. Processed {files_processed} files from batch directory {batch_name}.")
    return files_processed

if __name__ == "__main__":
    if len(sys.argv) != 3:
        print("Usage: python convert_hits.py <input_batch_directory> <output_directory>")
        print("  - input_batch_directory: Path to a single batch directory containing .tbl files")
        print("  - output_directory: Directory where the CSV files will be saved")
        print("\nNote: The output_directory should be the exact directory where you want the CSV files to be saved.")
        print("For example, if your input is 'hits/batch_1' and you want output in 'csv_hits/batch_1',")
        print("then specify 'csv_hits/batch_1' as the output directory, not just 'csv_hits'.")
        sys.exit(1)

    input_batch_dir = sys.argv[1]
    output_dir = sys.argv[2]

    # Verify that the input directory exists
    if not os.path.exists(input_batch_dir):
        print(f"Error: Input batch directory {input_batch_dir} does not exist.")
        sys.exit(1)

    # Verify that the input directory is a directory
    if not os.path.isdir(input_batch_dir):
        print(f"Error: {input_batch_dir} is not a directory.")
        sys.exit(1)

    # Process the batch directory
    process_batch_directory(input_batch_dir, output_dir)
