#!/bin/bash
# Script to process existing CSV files in the results/processed_hits directory

# Define paths
RESULTS_DIR="results"
PROCESSED_HITS_DIR="${RESULTS_DIR}/processed_hits"
GTDB_CLASSIFICATION="${RESULTS_DIR}/gtdbtk/gtdb_classification.csv"
OUTPUT_FILE="${RESULTS_DIR}/final_results.csv"
SCRIPT_PATH="bin/create_unique_df_hits_optimized.py"

# Create a temporary directory structure for the script
TEMP_DIR="temp_csv_hits"
rm -rf "${TEMP_DIR}"
mkdir -p "${TEMP_DIR}"

# Find all batch directories
echo "Looking for batch directories in ${PROCESSED_HITS_DIR}"
BATCH_DIRS=$(find "${PROCESSED_HITS_DIR}" -type d -name "batch_*" -maxdepth 1)
echo "Found batch directories: ${BATCH_DIRS}"

# Create corresponding directories in the temp directory and copy files
for BATCH_DIR in ${BATCH_DIRS}; do
    BATCH_NAME=$(basename "${BATCH_DIR}")
    DEST_DIR="${TEMP_DIR}/${BATCH_NAME}"
    mkdir -p "${DEST_DIR}"
    
    # Copy all CSV files from the batch directory to the temp directory
    echo "Copying CSV files from ${BATCH_DIR} to ${DEST_DIR}"
    find "${BATCH_DIR}" -name "*.csv" -exec cp {} "${DEST_DIR}/" \;
done

# List the contents of the temp directory
echo "Contents of ${TEMP_DIR}:"
find "${TEMP_DIR}" -type f | sort

# Run the create_unique_df_hits_optimized.py script
echo "Running: python ${SCRIPT_PATH} ${TEMP_DIR} ${GTDB_CLASSIFICATION} ${OUTPUT_FILE}"
python "${SCRIPT_PATH}" "${TEMP_DIR}" "${GTDB_CLASSIFICATION}" "${OUTPUT_FILE}"

# Check if the script was successful
if [ $? -eq 0 ]; then
    echo "Script completed successfully. Output saved to ${OUTPUT_FILE}"
    # Clean up
    echo "Cleaning up temporary directory ${TEMP_DIR}"
    rm -rf "${TEMP_DIR}"
    exit 0
else
    echo "Error running script"
    exit 1
fi
