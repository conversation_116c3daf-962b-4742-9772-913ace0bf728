May-27 15:11:05.961 [main] DEBUG nextflow.cli.Launcher - $> nextflow run main.nf --genome GCF_008369605.1.fna
May-27 15:11:06.056 [main] DEBUG nextflow.cli.CmdRun - N E X T F L O W  ~  version 25.04.2
May-27 15:11:06.076 [main] DEBUG nextflow.plugin.PluginsFacade - Setting up plugin manager > mode=prod; embedded=false; plugins-dir=/clusterfs/jgi/groups/science/homes/laureli/.nextflow/plugins; core-plugins: nf-amazon@2.15.0,nf-azure@1.16.0,nf-cloudcache@0.4.3,nf-codecommit@0.2.3,nf-console@1.2.1,nf-google@1.21.0,nf-k8s@1.0.0,nf-tower@1.11.2,nf-wave@1.12.1
May-27 15:11:06.129 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Enabled plugins: []
May-27 15:11:06.133 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Disabled plugins: []
May-27 15:11:06.135 [main] INFO  org.pf4j.DefaultPluginManager - PF4J version 3.12.0 in 'deployment' mode
May-27 15:11:06.152 [main] INFO  org.pf4j.AbstractPluginManager - No plugins
May-27 15:11:06.172 [main] DEBUG nextflow.config.ConfigBuilder - Found config local: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/nextflow.config
May-27 15:11:06.175 [main] DEBUG nextflow.config.ConfigBuilder - Parsing config file: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/nextflow.config
May-27 15:11:06.215 [main] DEBUG n.secret.LocalSecretsProvider - Secrets store: /clusterfs/jgi/groups/science/homes/laureli/.nextflow/secrets/store.json
May-27 15:11:06.218 [main] DEBUG nextflow.secret.SecretsLoader - Discovered secrets providers: [nextflow.secret.LocalSecretsProvider@1e6b9a95] - activable => nextflow.secret.LocalSecretsProvider@1e6b9a95
May-27 15:11:06.233 [main] DEBUG nextflow.config.ConfigBuilder - Applying config profile: `standard`
May-27 15:11:06.738 [main] DEBUG nextflow.cli.CmdRun - Applied DSL=2 from script declaration
May-27 15:11:06.754 [main] DEBUG nextflow.cli.CmdRun - Launching `main.nf` [happy_hirsch] DSL2 - revision: 572cf1876f
May-27 15:11:06.755 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins default=[]
May-27 15:11:06.756 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins resolved requirement=[]
May-27 15:11:06.815 [main] DEBUG nextflow.Session - Session UUID: c098a929-9f11-4daa-b450-9d4d725093d1
May-27 15:11:06.815 [main] DEBUG nextflow.Session - Run name: happy_hirsch
May-27 15:11:06.817 [main] DEBUG nextflow.Session - Executor pool size: 32
May-27 15:11:06.825 [main] DEBUG nextflow.file.FilePorter - File porter settings maxRetries=3; maxTransfers=50; pollTimeout=null
May-27 15:11:06.835 [main] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'FileTransfer' minSize=10; maxSize=96; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
May-27 15:11:06.856 [main] DEBUG nextflow.cli.CmdRun - 
  Version: 25.04.2 build 5947
  Created: 13-05-2025 21:58 UTC (14:58 PDT)
  System: Linux 4.18.0-553.5.1.el8_10.x86_64
  Runtime: Groovy 4.0.26 on OpenJDK 64-Bit Server VM 17.0.11-internal+0-adhoc..src
  Encoding: UTF-8 (UTF-8)
  Process: <EMAIL> [10.0.7.9]
  CPUs: 32 - Mem: 503.8 GB (46 GB) - Swap: 8 GB (0)
May-27 15:11:06.916 [main] DEBUG nextflow.Session - Work-dir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work [nfs]
May-27 15:11:06.950 [main] DEBUG nextflow.executor.ExecutorFactory - Extension executors providers=[]
May-27 15:11:06.959 [main] DEBUG nextflow.Session - Observer factory: DefaultObserverFactory
May-27 15:11:06.970 [main] DEBUG nextflow.Session - Observer factory (v2): LinObserverFactory
May-27 15:11:06.992 [main] DEBUG nextflow.cache.CacheFactory - Using Nextflow cache factory: nextflow.cache.DefaultCacheFactory
May-27 15:11:06.999 [main] DEBUG nextflow.util.CustomThreadPool - Creating default thread pool > poolSize: 33; maxThreads: 1000
May-27 15:11:07.227 [main] DEBUG nextflow.Session - Session start
May-27 15:11:07.880 [main] DEBUG nextflow.script.ScriptRunner - > Launching execution
May-27 15:11:07.914 [main] INFO  nextflow.Nextflow - 
==============================================
 GENOMIC DATA PROCESSING WORKFLOW
==============================================
 input genome: GCF_008369605.1.fna
 outdir     : /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/results
 gtdbtk_db  : /clusterfs/jgi/scratch/science/mgs/nelli/databases/gtdbtk/release220
 hmm_base_dir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/1_pangenomes_features/genera_pangenome_est_30_Sept/hmm_est_5_30_Sept
 processing batches: 1 to 66
 scaler path: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/robustscaler_enc1024_layers1.pkl
 model path : /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/robustscaler_enc1024_layers1.h5
 autoencoder output: encoded_features.csv
 ML model path: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/best_refined_model_RS.pkl
 prediction output: prediction_results.csv
 prediction version: 1
 skip taxonomic classification: false
==============================================

May-27 15:11:08.034 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:PRODIGAL` matches process PRODIGAL
May-27 15:11:08.044 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-27 15:11:08.045 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-27 15:11:08.061 [main] DEBUG nextflow.executor.Executor - [warm up] executor > local
May-27 15:11:08.068 [main] DEBUG n.processor.LocalPollingMonitor - Creating local task monitor for executor 'local' > cpus=32; memory=503.8 GB; capacity=32; pollInterval=100ms; dumpInterval=5m
May-27 15:11:08.069 [main] DEBUG n.processor.TaskPollingMonitor - >>> barrier register (monitor: local)
May-27 15:11:08.095 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'PRODIGAL': maxForks=0; fair=false; array=0
May-27 15:11:08.146 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:GTDBTK` matches process GTDBTK
May-27 15:11:08.147 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-27 15:11:08.156 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-27 15:11:08.164 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'GTDBTK': maxForks=0; fair=false; array=0
May-27 15:11:08.194 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:HMMSEARCH` matches process HMMSEARCH
May-27 15:11:08.211 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-27 15:11:08.211 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-27 15:11:08.219 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'HMMSEARCH': maxForks=0; fair=false; array=0
May-27 15:11:08.227 [main] DEBUG nextflow.processor.TaskProcessor - Creating *combiner* operator for each param(s) at index(es): [0, 1, 2]
May-27 15:11:08.247 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:PROCESS_HITS` matches process PROCESS_HITS
May-27 15:11:08.249 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-27 15:11:08.251 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-27 15:11:08.255 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'PROCESS_HITS': maxForks=0; fair=false; array=0
May-27 15:11:08.258 [main] DEBUG nextflow.processor.TaskProcessor - Creating *combiner* operator for each param(s) at index(es): [1]
May-27 15:11:08.276 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:CREATE_DATAFRAME` matches process CREATE_DATAFRAME
May-27 15:11:08.278 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-27 15:11:08.279 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-27 15:11:08.283 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'CREATE_DATAFRAME': maxForks=0; fair=false; array=0
May-27 15:11:08.294 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-27 15:11:08.295 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-27 15:11:08.301 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'AUTOENCODER': maxForks=0; fair=false; array=0
May-27 15:11:08.310 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-27 15:11:08.315 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-27 15:11:08.332 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'MODEL_PREDICT': maxForks=0; fair=false; array=0
May-27 15:11:08.335 [main] DEBUG nextflow.Session - Workflow process names [dsl2]: CREATE_DATAFRAME, MODEL_PREDICT, GTDBTK, HMMSEARCH, PRODIGAL, PROCESS_HITS, AUTOENCODER
May-27 15:11:08.338 [main] DEBUG nextflow.Session - Igniting dataflow network (21)
May-27 15:11:08.344 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > PRODIGAL
May-27 15:11:08.344 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > GTDBTK
May-27 15:11:08.350 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > HMMSEARCH
May-27 15:11:08.353 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > HMMSEARCH
May-27 15:11:08.359 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > PROCESS_HITS
May-27 15:11:08.361 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > PROCESS_HITS
May-27 15:11:08.363 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > CREATE_DATAFRAME
May-27 15:11:08.364 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > AUTOENCODER
May-27 15:11:08.372 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > MODEL_PREDICT
May-27 15:11:08.374 [main] DEBUG nextflow.script.ScriptRunner - Parsed script files:
  Script_f5dec23c1b3efd54: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/main.nf
May-27 15:11:08.376 [main] DEBUG nextflow.script.ScriptRunner - > Awaiting termination 
May-27 15:11:08.379 [main] DEBUG nextflow.Session - Session await
May-27 15:11:08.521 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:11:08.523 [Task submitter] INFO  nextflow.Session - [7f/b75a84] Submitted process > GTDBTK (1)
May-27 15:11:08.559 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:11:08.559 [Task submitter] INFO  nextflow.Session - [52/f42a82] Submitted process > PRODIGAL (1)
May-27 15:11:15.790 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 1; name: PRODIGAL (1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/52/f42a823f46ea0f9f1fc12c01a5ed15]
May-27 15:11:15.791 [Task monitor] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'TaskFinalizer' minSize=10; maxSize=96; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
May-27 15:11:15.819 [TaskFinalizer-1] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'PublishDir' minSize=10; maxSize=96; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
May-27 15:11:15.888 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:11:15.889 [Task submitter] INFO  nextflow.Session - [3a/e406c8] Submitted process > HMMSEARCH (batch_10)
May-27 15:11:28.419 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 12; name: HMMSEARCH (batch_10); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/3a/e406c8ad44a8876a1be073cda1e694]
May-27 15:11:28.450 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:11:28.451 [Task submitter] INFO  nextflow.Session - [40/b6dd09] Submitted process > HMMSEARCH (batch_17)
May-27 15:11:38.557 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 19; name: HMMSEARCH (batch_17); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/40/b6dd098d18e2b120b7ad381b8ee388]
May-27 15:11:38.579 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:11:38.580 [Task submitter] INFO  nextflow.Session - [a5/dcc454] Submitted process > HMMSEARCH (batch_23)
May-27 15:11:49.539 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 25; name: HMMSEARCH (batch_23); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a5/dcc454055a454c09b6b8bcafc06a0c]
May-27 15:11:49.558 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:11:49.559 [Task submitter] INFO  nextflow.Session - [81/3946fd] Submitted process > HMMSEARCH (batch_11)
May-27 15:11:59.695 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 13; name: HMMSEARCH (batch_11); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/81/3946fd4b2927a27b3bdc4078694bac]
May-27 15:11:59.715 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:11:59.716 [Task submitter] INFO  nextflow.Session - [db/25111a] Submitted process > HMMSEARCH (batch_20)
May-27 15:12:09.378 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 22; name: HMMSEARCH (batch_20); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/db/25111aef175b55b59fff07ddacf1ef]
May-27 15:12:09.403 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:12:09.403 [Task submitter] INFO  nextflow.Session - [49/70731f] Submitted process > HMMSEARCH (batch_5)
May-27 15:12:17.161 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 7; name: HMMSEARCH (batch_5); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/49/70731fc04544b055e7b093e5b38e4f]
May-27 15:12:17.179 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:12:17.180 [Task submitter] INFO  nextflow.Session - [b8/0529ce] Submitted process > HMMSEARCH (batch_28)
May-27 15:12:24.430 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 30; name: HMMSEARCH (batch_28); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b8/0529ceee81a8b723ed7077de044bf6]
May-27 15:12:24.442 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:12:24.443 [Task submitter] INFO  nextflow.Session - [4e/2fcfaa] Submitted process > HMMSEARCH (batch_3)
May-27 15:12:31.742 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 5; name: HMMSEARCH (batch_3); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/4e/2fcfaa2e688a76230b03a29e85dce6]
May-27 15:12:31.754 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:12:31.754 [Task submitter] INFO  nextflow.Session - [9f/6bdcb6] Submitted process > HMMSEARCH (batch_24)
May-27 15:12:39.052 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 26; name: HMMSEARCH (batch_24); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/9f/6bdcb63f564158a7bce8ace1574abf]
May-27 15:12:39.070 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:12:39.071 [Task submitter] INFO  nextflow.Session - [88/b5afb0] Submitted process > HMMSEARCH (batch_4)
May-27 15:12:48.048 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 6; name: HMMSEARCH (batch_4); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/88/b5afb08ab1649b057758a9b77fdac1]
May-27 15:12:48.063 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:12:48.064 [Task submitter] INFO  nextflow.Session - [6b/73c2e6] Submitted process > HMMSEARCH (batch_31)
May-27 15:12:56.630 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 33; name: HMMSEARCH (batch_31); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/6b/73c2e65d057e61a2cb73779d2a4391]
May-27 15:12:56.650 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:12:56.651 [Task submitter] INFO  nextflow.Session - [4f/aa097b] Submitted process > HMMSEARCH (batch_7)
May-27 15:13:04.876 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 9; name: HMMSEARCH (batch_7); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/4f/aa097b5dcb7881fc88a131aa1ae702]
May-27 15:13:04.896 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:13:04.896 [Task submitter] INFO  nextflow.Session - [ad/fe0333] Submitted process > HMMSEARCH (batch_16)
May-27 15:13:12.926 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 18; name: HMMSEARCH (batch_16); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ad/fe0333dbc0aa25d8a3633ebb212435]
May-27 15:13:12.953 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:13:12.954 [Task submitter] INFO  nextflow.Session - [d4/9cd36a] Submitted process > HMMSEARCH (batch_29)
May-27 15:13:24.116 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 31; name: HMMSEARCH (batch_29); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/d4/9cd36a0af924968f48f850ecb7a379]
May-27 15:13:24.159 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:13:24.159 [Task submitter] INFO  nextflow.Session - [3a/8a8cb8] Submitted process > HMMSEARCH (batch_44)
May-27 15:13:36.915 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 46; name: HMMSEARCH (batch_44); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/3a/8a8cb8342a7f343ac37d5964567ccd]
May-27 15:13:36.934 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:13:36.934 [Task submitter] INFO  nextflow.Session - [f0/9b7462] Submitted process > HMMSEARCH (batch_32)
May-27 15:13:45.885 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 34; name: HMMSEARCH (batch_32); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f0/9b7462f8b0622fc7f6aee91a7edd31]
May-27 15:13:45.916 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:13:45.916 [Task submitter] INFO  nextflow.Session - [5a/abc9b2] Submitted process > HMMSEARCH (batch_8)
May-27 15:13:54.841 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 10; name: HMMSEARCH (batch_8); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/5a/abc9b2dc13dbf961d4d9c437d7e05e]
May-27 15:13:54.854 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:13:54.854 [Task submitter] INFO  nextflow.Session - [9b/633413] Submitted process > HMMSEARCH (batch_14)
May-27 15:14:05.218 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 16; name: HMMSEARCH (batch_14); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/9b/633413f8e14092f7f4848f2912c7ad]
May-27 15:14:05.241 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:14:05.241 [Task submitter] INFO  nextflow.Session - [78/02176a] Submitted process > HMMSEARCH (batch_18)
May-27 15:14:16.040 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 20; name: HMMSEARCH (batch_18); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/78/02176a0afa15f988f3d4cf7796f5e7]
May-27 15:14:16.088 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:14:16.088 [Task submitter] INFO  nextflow.Session - [4e/f9f718] Submitted process > HMMSEARCH (batch_51)
May-27 15:14:25.749 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 53; name: HMMSEARCH (batch_51); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/4e/f9f718d21ff8e4be867f19963782c6]
May-27 15:14:25.776 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:14:25.776 [Task submitter] INFO  nextflow.Session - [cc/fde8ac] Submitted process > HMMSEARCH (batch_27)
May-27 15:14:35.865 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 29; name: HMMSEARCH (batch_27); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/cc/fde8acfa749c12edf02544133a0dab]
May-27 15:14:35.900 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:14:35.900 [Task submitter] INFO  nextflow.Session - [5d/406aa8] Submitted process > HMMSEARCH (batch_30)
May-27 15:14:44.441 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 32; name: HMMSEARCH (batch_30); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/5d/406aa824fa4cf1e521878f5565001c]
May-27 15:14:44.468 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:14:44.468 [Task submitter] INFO  nextflow.Session - [29/ff1717] Submitted process > HMMSEARCH (batch_6)
May-27 15:14:52.960 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 8; name: HMMSEARCH (batch_6); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/29/ff1717626bdc2e965654c825d6017a]
May-27 15:14:52.981 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:14:52.982 [Task submitter] INFO  nextflow.Session - [65/b22a39] Submitted process > HMMSEARCH (batch_19)
May-27 15:15:04.223 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 21; name: HMMSEARCH (batch_19); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/65/b22a3925f2d182f4c2e3db9edb8dea]
May-27 15:15:04.239 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:15:04.240 [Task submitter] INFO  nextflow.Session - [d6/8c7250] Submitted process > HMMSEARCH (batch_13)
May-27 15:15:13.143 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 15; name: HMMSEARCH (batch_13); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/d6/8c72501f5f5358f866f64eae01937f]
May-27 15:15:13.155 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:15:13.155 [Task submitter] INFO  nextflow.Session - [78/550b22] Submitted process > HMMSEARCH (batch_53)
May-27 15:15:19.707 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 55; name: HMMSEARCH (batch_53); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/78/550b226f1ac54ca6ae4cbba88cb528]
May-27 15:15:19.716 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:15:19.717 [Task submitter] INFO  nextflow.Session - [e1/8756c2] Submitted process > HMMSEARCH (batch_2)
May-27 15:15:26.568 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 4; name: HMMSEARCH (batch_2); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e1/8756c25d1ccd52095ece1558d56abf]
May-27 15:15:26.591 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:15:26.591 [Task submitter] INFO  nextflow.Session - [36/2d125f] Submitted process > HMMSEARCH (batch_26)
May-27 15:15:36.249 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 28; name: HMMSEARCH (batch_26); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/36/2d125f249888ad4341496aff900be0]
May-27 15:15:36.281 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:15:36.281 [Task submitter] INFO  nextflow.Session - [ed/a12007] Submitted process > HMMSEARCH (batch_22)
May-27 15:15:43.106 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 24; name: HMMSEARCH (batch_22); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ed/a12007de4374258cb63dc505fcb930]
May-27 15:15:43.121 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:15:43.121 [Task submitter] INFO  nextflow.Session - [85/19d0d5] Submitted process > HMMSEARCH (batch_25)
May-27 15:15:49.596 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 27; name: HMMSEARCH (batch_25); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/85/19d0d54c1f4b19976731532bbf43f2]
May-27 15:15:49.617 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:15:49.617 [Task submitter] INFO  nextflow.Session - [69/96ad6a] Submitted process > HMMSEARCH (batch_1)
May-27 15:15:57.257 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 3; name: HMMSEARCH (batch_1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/69/96ad6ac9022bad71bcf7a2230ee00c]
May-27 15:15:57.268 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:15:57.269 [Task submitter] INFO  nextflow.Session - [45/97965a] Submitted process > HMMSEARCH (batch_15)
May-27 15:16:04.529 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 17; name: HMMSEARCH (batch_15); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/45/97965aeddb96a21ad41b64e155d70a]
May-27 15:16:04.541 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:16:04.542 [Task submitter] INFO  nextflow.Session - [ef/e069c4] Submitted process > HMMSEARCH (batch_21)
May-27 15:16:08.236 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 2 -- submitted tasks are shown below
~> TaskHandler[id: 2; name: GTDBTK (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/7f/b75a841242c48c0225644649a081bb]
~> TaskHandler[id: 23; name: HMMSEARCH (batch_21); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ef/e069c4978c87fc93d9c260781b8407]
May-27 15:16:12.087 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 23; name: HMMSEARCH (batch_21); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ef/e069c4978c87fc93d9c260781b8407]
May-27 15:16:12.112 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:16:12.113 [Task submitter] INFO  nextflow.Session - [34/eb0f06] Submitted process > HMMSEARCH (batch_12)
May-27 15:16:16.116 [Task submitter] DEBUG n.processor.TaskPollingMonitor - %% executor local > tasks in the submission queue: 65 -- tasks to be submitted are shown below
~> TaskHandler[id: 66; name: HMMSEARCH (batch_64); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/5d/dbde794f21effd53bea9692daa2f80]
~> TaskHandler[id: 36; name: HMMSEARCH (batch_34); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/42/68778a2ce9a40629b46dd7975f3fdb]
~> TaskHandler[id: 11; name: HMMSEARCH (batch_9); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/68/bed45ecc3bb270fc853038501d5815]
~> TaskHandler[id: 48; name: HMMSEARCH (batch_46); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/56/c83780693b41375244b4b08e94801f]
~> TaskHandler[id: 67; name: HMMSEARCH (batch_65); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/8e/a5c86b02b5c459523a5a056265de8e]
~> TaskHandler[id: 51; name: HMMSEARCH (batch_49); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b9/8e5d9f67732f38005fe17559feff47]
~> TaskHandler[id: 38; name: HMMSEARCH (batch_36); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/26/6a81f14cd521a50e8a40acb31f7168]
~> TaskHandler[id: 49; name: HMMSEARCH (batch_47); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/46/7643aa3a5dd590ea372aa45533bb16]
~> TaskHandler[id: 40; name: HMMSEARCH (batch_38); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ce/aea43df1c6495cccc5f3a7738aaf2b]
~> TaskHandler[id: 63; name: HMMSEARCH (batch_61); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/61/ba2dd08d6635c0a5bc369f17accde8]
.. remaining tasks omitted.
May-27 15:16:18.914 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 14; name: HMMSEARCH (batch_12); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/34/eb0f06b26f8c00dbb44ee3a32f2cf5]
May-27 15:16:18.928 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:16:18.928 [Task submitter] INFO  nextflow.Session - [5d/dbde79] Submitted process > HMMSEARCH (batch_64)
May-27 15:16:26.279 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 66; name: HMMSEARCH (batch_64); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/5d/dbde794f21effd53bea9692daa2f80]
May-27 15:16:26.292 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:16:26.292 [Task submitter] INFO  nextflow.Session - [42/68778a] Submitted process > HMMSEARCH (batch_34)
May-27 15:16:33.326 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 36; name: HMMSEARCH (batch_34); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/42/68778a2ce9a40629b46dd7975f3fdb]
May-27 15:16:33.340 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:16:33.340 [Task submitter] INFO  nextflow.Session - [68/bed45e] Submitted process > HMMSEARCH (batch_9)
May-27 15:16:40.134 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 11; name: HMMSEARCH (batch_9); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/68/bed45ecc3bb270fc853038501d5815]
May-27 15:16:40.161 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:16:40.161 [Task submitter] INFO  nextflow.Session - [56/c83780] Submitted process > HMMSEARCH (batch_46)
May-27 15:16:47.311 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 48; name: HMMSEARCH (batch_46); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/56/c83780693b41375244b4b08e94801f]
May-27 15:16:47.326 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:16:47.327 [Task submitter] INFO  nextflow.Session - [8e/a5c86b] Submitted process > HMMSEARCH (batch_65)
May-27 15:16:54.085 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 67; name: HMMSEARCH (batch_65); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/8e/a5c86b02b5c459523a5a056265de8e]
May-27 15:16:54.106 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:16:54.107 [Task submitter] INFO  nextflow.Session - [b9/8e5d9f] Submitted process > HMMSEARCH (batch_49)
May-27 15:17:00.991 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 51; name: HMMSEARCH (batch_49); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b9/8e5d9f67732f38005fe17559feff47]
May-27 15:17:01.002 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:17:01.003 [Task submitter] INFO  nextflow.Session - [26/6a81f1] Submitted process > HMMSEARCH (batch_36)
May-27 15:17:08.305 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 38; name: HMMSEARCH (batch_36); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/26/6a81f14cd521a50e8a40acb31f7168]
May-27 15:17:08.316 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:17:08.317 [Task submitter] INFO  nextflow.Session - [46/7643aa] Submitted process > HMMSEARCH (batch_47)
May-27 15:17:16.077 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 49; name: HMMSEARCH (batch_47); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/46/7643aa3a5dd590ea372aa45533bb16]
May-27 15:17:16.099 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:17:16.099 [Task submitter] INFO  nextflow.Session - [ce/aea43d] Submitted process > HMMSEARCH (batch_38)
May-27 15:17:23.534 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 40; name: HMMSEARCH (batch_38); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ce/aea43df1c6495cccc5f3a7738aaf2b]
May-27 15:17:23.543 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:17:23.543 [Task submitter] INFO  nextflow.Session - [61/ba2dd0] Submitted process > HMMSEARCH (batch_61)
May-27 15:17:30.396 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 63; name: HMMSEARCH (batch_61); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/61/ba2dd08d6635c0a5bc369f17accde8]
May-27 15:17:30.407 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:17:30.407 [Task submitter] INFO  nextflow.Session - [e8/05bb31] Submitted process > HMMSEARCH (batch_45)
May-27 15:17:37.674 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 47; name: HMMSEARCH (batch_45); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e8/05bb31ea58e93c420b588c421f039d]
May-27 15:17:37.690 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:17:37.690 [Task submitter] INFO  nextflow.Session - [13/a89173] Submitted process > HMMSEARCH (batch_58)
May-27 15:17:44.769 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 60; name: HMMSEARCH (batch_58); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/13/a8917305019730cf54836977671ebb]
May-27 15:17:44.793 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:17:44.794 [Task submitter] INFO  nextflow.Session - [fb/c82f67] Submitted process > HMMSEARCH (batch_56)
May-27 15:17:53.636 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 58; name: HMMSEARCH (batch_56); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/fb/c82f674c3b3a79d04ddfa6e81189a0]
May-27 15:17:53.652 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:17:53.653 [Task submitter] INFO  nextflow.Session - [48/7b9f8a] Submitted process > HMMSEARCH (batch_59)
May-27 15:18:01.208 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 61; name: HMMSEARCH (batch_59); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/48/7b9f8a5ab8990dac298564b9485d6b]
May-27 15:18:01.223 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:18:01.223 [Task submitter] INFO  nextflow.Session - [57/23456f] Submitted process > HMMSEARCH (batch_35)
May-27 15:18:08.123 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 37; name: HMMSEARCH (batch_35); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/57/23456fa6031b2e50fca715772cdc97]
May-27 15:18:08.135 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:18:08.135 [Task submitter] INFO  nextflow.Session - [bd/12a0d4] Submitted process > HMMSEARCH (batch_37)
May-27 15:18:15.463 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 39; name: HMMSEARCH (batch_37); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/bd/12a0d484bdfd755f9e591f7cc3878e]
May-27 15:18:15.481 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:18:15.481 [Task submitter] INFO  nextflow.Session - [a3/91b95c] Submitted process > HMMSEARCH (batch_52)
May-27 15:18:23.732 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 54; name: HMMSEARCH (batch_52); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a3/91b95ceef930cc589cb3b0c01bd3e8]
May-27 15:18:23.774 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:18:23.774 [Task submitter] INFO  nextflow.Session - [e5/178052] Submitted process > HMMSEARCH (batch_40)
May-27 15:18:31.947 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 42; name: HMMSEARCH (batch_40); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e5/17805223d90975b704853a869ec976]
May-27 15:18:31.960 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:18:31.960 [Task submitter] INFO  nextflow.Session - [e5/3a4381] Submitted process > HMMSEARCH (batch_62)
May-27 15:18:40.279 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 64; name: HMMSEARCH (batch_62); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e5/3a4381060a9de6ea85dc49120de30a]
May-27 15:18:40.293 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:18:40.294 [Task submitter] INFO  nextflow.Session - [39/7c2b58] Submitted process > HMMSEARCH (batch_66)
May-27 15:18:41.328 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 68; name: HMMSEARCH (batch_66); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/39/7c2b586bc7e305337c9b4e4765115f]
May-27 15:18:41.347 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:18:41.347 [Task submitter] INFO  nextflow.Session - [ea/d50513] Submitted process > HMMSEARCH (batch_43)
May-27 15:18:50.303 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 45; name: HMMSEARCH (batch_43); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ea/d50513eb68ea60857505a9887b2ecb]
May-27 15:18:50.313 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:18:50.313 [Task submitter] INFO  nextflow.Session - [04/23490f] Submitted process > HMMSEARCH (batch_42)
May-27 15:18:58.385 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 44; name: HMMSEARCH (batch_42); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/04/23490f2cc1209cc0ad4936622c0e76]
May-27 15:18:58.396 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:18:58.397 [Task submitter] INFO  nextflow.Session - [f8/b437d7] Submitted process > HMMSEARCH (batch_50)
May-27 15:19:05.354 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 52; name: HMMSEARCH (batch_50); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f8/b437d75acfce23549239c697ba8dc3]
May-27 15:19:05.366 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:19:05.366 [Task submitter] INFO  nextflow.Session - [90/a225cf] Submitted process > HMMSEARCH (batch_57)
May-27 15:19:13.821 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 59; name: HMMSEARCH (batch_57); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/90/a225cf776cd80bf0f411d61c941b24]
May-27 15:19:13.842 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:19:13.842 [Task submitter] INFO  nextflow.Session - [d0/648129] Submitted process > HMMSEARCH (batch_48)
May-27 15:19:23.866 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 50; name: HMMSEARCH (batch_48); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/d0/6481292a8f698a68aee3e037e5cf21]
May-27 15:19:23.881 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:19:23.881 [Task submitter] INFO  nextflow.Session - [86/b250ff] Submitted process > HMMSEARCH (batch_54)
May-27 15:19:33.898 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 56; name: HMMSEARCH (batch_54); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/86/b250ff153274b8f9fd7022bf179522]
May-27 15:19:33.918 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:19:33.918 [Task submitter] INFO  nextflow.Session - [25/81a0c9] Submitted process > HMMSEARCH (batch_33)
May-27 15:19:41.379 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 35; name: HMMSEARCH (batch_33); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/25/81a0c911e7228d2f849c73ce715ed1]
May-27 15:19:41.402 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:19:41.403 [Task submitter] INFO  nextflow.Session - [d9/d1d00a] Submitted process > HMMSEARCH (batch_60)
May-27 15:19:50.138 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 62; name: HMMSEARCH (batch_60); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/d9/d1d00a01142ec8dddc48b006b18191]
May-27 15:19:50.162 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:19:50.162 [Task submitter] INFO  nextflow.Session - [fd/6aa1aa] Submitted process > HMMSEARCH (batch_41)
May-27 15:19:59.646 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 43; name: HMMSEARCH (batch_41); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/fd/6aa1aaf8a2de6519053b6e6390ad46]
May-27 15:19:59.673 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:19:59.673 [Task submitter] INFO  nextflow.Session - [00/f15cef] Submitted process > HMMSEARCH (batch_55)
May-27 15:20:09.691 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 57; name: HMMSEARCH (batch_55); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/00/f15cef1ed22a34a1fd11d00e1dbdd4]
May-27 15:20:09.729 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:20:09.730 [Task submitter] INFO  nextflow.Session - [3e/114685] Submitted process > HMMSEARCH (batch_39)
May-27 15:20:19.529 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 41; name: HMMSEARCH (batch_39); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/3e/11468570d39b3597c14598fc5af54e]
May-27 15:20:19.552 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:20:19.555 [Task submitter] INFO  nextflow.Session - [a6/95350f] Submitted process > HMMSEARCH (batch_63)
May-27 15:20:30.802 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 65; name: HMMSEARCH (batch_63); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a6/95350f6a18abc23b03c23ddf3cfc1e]
May-27 15:20:30.829 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:20:30.829 [Task submitter] INFO  nextflow.Session - [2d/178a4e] Submitted process > PROCESS_HITS (batch_10)
May-27 15:20:30.849 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:20:30.849 [Task submitter] INFO  nextflow.Session - [a7/098626] Submitted process > PROCESS_HITS (batch_17)
May-27 15:20:30.863 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:20:30.863 [Task submitter] INFO  nextflow.Session - [88/0b0255] Submitted process > PROCESS_HITS (batch_23)
May-27 15:20:30.874 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:20:30.874 [Task submitter] INFO  nextflow.Session - [16/7f289e] Submitted process > PROCESS_HITS (batch_11)
May-27 15:20:30.893 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:20:30.894 [Task submitter] INFO  nextflow.Session - [52/89649a] Submitted process > PROCESS_HITS (batch_20)
May-27 15:20:30.919 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:20:30.919 [Task submitter] INFO  nextflow.Session - [36/fea838] Submitted process > PROCESS_HITS (batch_5)
May-27 15:20:30.939 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:20:30.939 [Task submitter] INFO  nextflow.Session - [c0/b1d650] Submitted process > PROCESS_HITS (batch_28)
May-27 15:20:30.958 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:20:30.958 [Task submitter] INFO  nextflow.Session - [8e/c9da21] Submitted process > PROCESS_HITS (batch_3)
May-27 15:20:52.009 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 73; name: PROCESS_HITS (batch_20); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/52/89649a5e69aeb5c76f8c9e71327525]
May-27 15:20:52.032 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 72; name: PROCESS_HITS (batch_11); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/16/7f289ec207eedc31811289ab7c1032]
May-27 15:20:52.042 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:20:52.042 [Task submitter] INFO  nextflow.Session - [2b/996176] Submitted process > PROCESS_HITS (batch_24)
May-27 15:20:52.089 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:20:52.090 [Task submitter] INFO  nextflow.Session - [79/1ab7c5] Submitted process > PROCESS_HITS (batch_4)
May-27 15:20:52.137 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 71; name: PROCESS_HITS (batch_23); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/88/0b02555d9fe9fa97a82a348df5df4c]
May-27 15:20:52.176 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:20:52.177 [Task submitter] INFO  nextflow.Session - [84/26213d] Submitted process > PROCESS_HITS (batch_31)
May-27 15:20:52.397 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 69; name: PROCESS_HITS (batch_10); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/2d/178a4ee64ca0b0fbee7548172e0e06]
May-27 15:20:52.411 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 76; name: PROCESS_HITS (batch_3); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/8e/c9da2132528f3530f2d2ee8fe86173]
May-27 15:20:52.440 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:20:52.441 [Task submitter] INFO  nextflow.Session - [8e/9b581f] Submitted process > PROCESS_HITS (batch_7)
May-27 15:20:52.467 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:20:52.467 [Task submitter] INFO  nextflow.Session - [fc/118fda] Submitted process > PROCESS_HITS (batch_16)
May-27 15:20:52.604 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 75; name: PROCESS_HITS (batch_28); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c0/b1d65068f839da6979a19ae42f4780]
May-27 15:20:52.622 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 74; name: PROCESS_HITS (batch_5); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/36/fea838af41919bd0109c4dc1f4aee6]
May-27 15:20:52.641 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:20:52.642 [Task submitter] INFO  nextflow.Session - [38/1c3b5c] Submitted process > PROCESS_HITS (batch_29)
May-27 15:20:52.667 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:20:52.668 [Task submitter] INFO  nextflow.Session - [91/3d96bd] Submitted process > PROCESS_HITS (batch_44)
May-27 15:20:53.103 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 70; name: PROCESS_HITS (batch_17); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a7/098626341905291c0304c79a0b495d]
May-27 15:20:53.125 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:20:53.126 [Task submitter] INFO  nextflow.Session - [0f/270d7b] Submitted process > PROCESS_HITS (batch_32)
May-27 15:21:08.318 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 9 -- submitted tasks are shown below
~> TaskHandler[id: 2; name: GTDBTK (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/7f/b75a841242c48c0225644649a081bb]
~> TaskHandler[id: 77; name: PROCESS_HITS (batch_24); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/2b/9961764d4ea11ad33bce0dc91b2bd9]
~> TaskHandler[id: 78; name: PROCESS_HITS (batch_4); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/79/1ab7c5f7da491739e147b9e55fdd3a]
~> TaskHandler[id: 79; name: PROCESS_HITS (batch_31); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/84/26213dd45daa7c34e0ebf672abf1f2]
~> TaskHandler[id: 80; name: PROCESS_HITS (batch_7); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/8e/9b581f37b6d6e76c7631ee85292c1c]
~> TaskHandler[id: 81; name: PROCESS_HITS (batch_16); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/fc/118fda40f0dea01c4a73a723a07699]
~> TaskHandler[id: 82; name: PROCESS_HITS (batch_29); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/38/1c3b5c330ad9ec4f300979e44a5794]
~> TaskHandler[id: 83; name: PROCESS_HITS (batch_44); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/91/3d96bd3021185dc8202390b5352586]
~> TaskHandler[id: 84; name: PROCESS_HITS (batch_32); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/0f/270d7b9b7e75b23fa1018ff12f118f]
May-27 15:21:12.751 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 78; name: PROCESS_HITS (batch_4); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/79/1ab7c5f7da491739e147b9e55fdd3a]
May-27 15:21:12.781 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:21:12.781 [Task submitter] INFO  nextflow.Session - [01/d1d857] Submitted process > PROCESS_HITS (batch_8)
May-27 15:21:13.300 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 81; name: PROCESS_HITS (batch_16); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/fc/118fda40f0dea01c4a73a723a07699]
May-27 15:21:13.315 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:21:13.316 [Task submitter] INFO  nextflow.Session - [99/d54105] Submitted process > PROCESS_HITS (batch_14)
May-27 15:21:13.333 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 77; name: PROCESS_HITS (batch_24); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/2b/9961764d4ea11ad33bce0dc91b2bd9]
May-27 15:21:13.351 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:21:13.352 [Task submitter] INFO  nextflow.Session - [ec/07b03e] Submitted process > PROCESS_HITS (batch_18)
May-27 15:21:13.650 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 79; name: PROCESS_HITS (batch_31); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/84/26213dd45daa7c34e0ebf672abf1f2]
May-27 15:21:13.690 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:21:13.691 [Task submitter] INFO  nextflow.Session - [24/4ad240] Submitted process > PROCESS_HITS (batch_51)
May-27 15:21:13.779 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 80; name: PROCESS_HITS (batch_7); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/8e/9b581f37b6d6e76c7631ee85292c1c]
May-27 15:21:13.801 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:21:13.802 [Task submitter] INFO  nextflow.Session - [a6/7593bc] Submitted process > PROCESS_HITS (batch_27)
May-27 15:21:13.856 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 83; name: PROCESS_HITS (batch_44); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/91/3d96bd3021185dc8202390b5352586]
May-27 15:21:13.883 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:21:13.884 [Task submitter] INFO  nextflow.Session - [76/91a9fb] Submitted process > PROCESS_HITS (batch_30)
May-27 15:21:13.900 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 82; name: PROCESS_HITS (batch_29); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/38/1c3b5c330ad9ec4f300979e44a5794]
May-27 15:21:13.948 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:21:13.948 [Task submitter] INFO  nextflow.Session - [f8/997be6] Submitted process > PROCESS_HITS (batch_6)
May-27 15:21:14.950 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 84; name: PROCESS_HITS (batch_32); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/0f/270d7b9b7e75b23fa1018ff12f118f]
May-27 15:21:14.981 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:21:14.981 [Task submitter] INFO  nextflow.Session - [30/258304] Submitted process > PROCESS_HITS (batch_19)
May-27 15:21:16.983 [Task submitter] DEBUG n.processor.TaskPollingMonitor - %% executor local > tasks in the submission queue: 42 -- tasks to be submitted are shown below
~> TaskHandler[id: 93; name: PROCESS_HITS (batch_13); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/43/a6170295272f15ef8cbc01eb867905]
~> TaskHandler[id: 94; name: PROCESS_HITS (batch_53); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/93/679f3ef31146daae543a24da85c6a8]
~> TaskHandler[id: 95; name: PROCESS_HITS (batch_2); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e0/42d71773ef9ab469247c85556538f4]
~> TaskHandler[id: 96; name: PROCESS_HITS (batch_26); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/12/c52af240bdc6209aff8338e3ef0709]
~> TaskHandler[id: 97; name: PROCESS_HITS (batch_22); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/97/e76b09916748f3c3af0af6353bf194]
~> TaskHandler[id: 98; name: PROCESS_HITS (batch_25); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/be/604cb0eb2cf57b0fe90ae9e814797e]
~> TaskHandler[id: 99; name: PROCESS_HITS (batch_1); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ed/5a49de43e13646cfd934d8f732f62e]
~> TaskHandler[id: 100; name: PROCESS_HITS (batch_15); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/13/c998895c0023edae768452eb17985d]
~> TaskHandler[id: 101; name: PROCESS_HITS (batch_21); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/2f/f2f9b580fdc3995f46bd65a8d15fad]
~> TaskHandler[id: 102; name: PROCESS_HITS (batch_12); status: NEW; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a5/809143d17e0c4dfb685cffc246097b]
.. remaining tasks omitted.
May-27 15:21:38.836 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 86; name: PROCESS_HITS (batch_14); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/99/d5410598b5cd0a8f235183f45f102e]
May-27 15:21:38.873 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:21:38.874 [Task submitter] INFO  nextflow.Session - [43/a61702] Submitted process > PROCESS_HITS (batch_13)
May-27 15:21:39.030 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 85; name: PROCESS_HITS (batch_8); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/01/d1d857b2b88cd45011bf4a6d863b85]
May-27 15:21:39.078 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:21:39.078 [Task submitter] INFO  nextflow.Session - [93/679f3e] Submitted process > PROCESS_HITS (batch_53)
May-27 15:21:39.174 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 87; name: PROCESS_HITS (batch_18); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ec/07b03ed804f387c4230d335fc2e2d8]
May-27 15:21:39.212 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:21:39.213 [Task submitter] INFO  nextflow.Session - [e0/42d717] Submitted process > PROCESS_HITS (batch_2)
May-27 15:21:39.720 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 90; name: PROCESS_HITS (batch_30); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/76/91a9fb783c366ccc4c293882f8035f]
May-27 15:21:39.742 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:21:39.742 [Task submitter] INFO  nextflow.Session - [12/c52af2] Submitted process > PROCESS_HITS (batch_26)
May-27 15:21:39.917 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 88; name: PROCESS_HITS (batch_51); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/24/4ad24023850e1d1dd23312d1facbb8]
May-27 15:21:39.950 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:21:39.951 [Task submitter] INFO  nextflow.Session - [97/e76b09] Submitted process > PROCESS_HITS (batch_22)
May-27 15:21:40.185 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 91; name: PROCESS_HITS (batch_6); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f8/997be6ccabb40a6b9df5a6f37e4faa]
May-27 15:21:40.215 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:21:40.215 [Task submitter] INFO  nextflow.Session - [be/604cb0] Submitted process > PROCESS_HITS (batch_25)
May-27 15:21:40.653 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 89; name: PROCESS_HITS (batch_27); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a6/7593bc3eb21bc026c205a61ca0a504]
May-27 15:21:40.709 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:21:40.709 [Task submitter] INFO  nextflow.Session - [ed/5a49de] Submitted process > PROCESS_HITS (batch_1)
May-27 15:21:41.257 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 92; name: PROCESS_HITS (batch_19); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/30/2583041b949c7436a45ba437bc8399]
May-27 15:21:41.271 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:21:41.272 [Task submitter] INFO  nextflow.Session - [13/c99889] Submitted process > PROCESS_HITS (batch_15)
May-27 15:22:01.130 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 94; name: PROCESS_HITS (batch_53); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/93/679f3ef31146daae543a24da85c6a8]
May-27 15:22:01.157 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:22:01.157 [Task submitter] INFO  nextflow.Session - [2f/f2f9b5] Submitted process > PROCESS_HITS (batch_21)
May-27 15:22:01.695 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 96; name: PROCESS_HITS (batch_26); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/12/c52af240bdc6209aff8338e3ef0709]
May-27 15:22:01.745 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:22:01.745 [Task submitter] INFO  nextflow.Session - [a5/809143] Submitted process > PROCESS_HITS (batch_12)
May-27 15:22:01.975 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 93; name: PROCESS_HITS (batch_13); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/43/a6170295272f15ef8cbc01eb867905]
May-27 15:22:01.995 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:22:01.995 [Task submitter] INFO  nextflow.Session - [e4/66c88d] Submitted process > PROCESS_HITS (batch_64)
May-27 15:22:02.155 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 95; name: PROCESS_HITS (batch_2); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e0/42d71773ef9ab469247c85556538f4]
May-27 15:22:02.175 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:22:02.175 [Task submitter] INFO  nextflow.Session - [c4/fcdc24] Submitted process > PROCESS_HITS (batch_34)
May-27 15:22:02.941 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 100; name: PROCESS_HITS (batch_15); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/13/c998895c0023edae768452eb17985d]
May-27 15:22:02.971 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:22:02.971 [Task submitter] INFO  nextflow.Session - [af/834202] Submitted process > PROCESS_HITS (batch_9)
May-27 15:22:03.094 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 98; name: PROCESS_HITS (batch_25); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/be/604cb0eb2cf57b0fe90ae9e814797e]
May-27 15:22:03.113 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:22:03.113 [Task submitter] INFO  nextflow.Session - [e9/54f5a8] Submitted process > PROCESS_HITS (batch_46)
May-27 15:22:03.131 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 97; name: PROCESS_HITS (batch_22); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/97/e76b09916748f3c3af0af6353bf194]
May-27 15:22:03.148 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:22:03.148 [Task submitter] INFO  nextflow.Session - [70/ae6b5e] Submitted process > PROCESS_HITS (batch_65)
May-27 15:22:03.302 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 99; name: PROCESS_HITS (batch_1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/ed/5a49de43e13646cfd934d8f732f62e]
May-27 15:22:03.325 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:22:03.325 [Task submitter] INFO  nextflow.Session - [41/0fb856] Submitted process > PROCESS_HITS (batch_49)
May-27 15:22:25.069 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 101; name: PROCESS_HITS (batch_21); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/2f/f2f9b580fdc3995f46bd65a8d15fad]
May-27 15:22:25.097 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:22:25.098 [Task submitter] INFO  nextflow.Session - [6e/dbea06] Submitted process > PROCESS_HITS (batch_36)
May-27 15:22:25.615 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 102; name: PROCESS_HITS (batch_12); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/a5/809143d17e0c4dfb685cffc246097b]
May-27 15:22:25.635 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:22:25.635 [Task submitter] INFO  nextflow.Session - [12/243015] Submitted process > PROCESS_HITS (batch_47)
May-27 15:22:26.096 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 104; name: PROCESS_HITS (batch_34); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c4/fcdc248acd1908fd753e596996025b]
May-27 15:22:26.130 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:22:26.130 [Task submitter] INFO  nextflow.Session - [cd/b3c41a] Submitted process > PROCESS_HITS (batch_38)
May-27 15:22:26.376 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 103; name: PROCESS_HITS (batch_64); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e4/66c88d58916904ea45661524fa6ce7]
May-27 15:22:26.405 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:22:26.406 [Task submitter] INFO  nextflow.Session - [dc/bd2e0e] Submitted process > PROCESS_HITS (batch_61)
May-27 15:22:27.006 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 105; name: PROCESS_HITS (batch_9); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/af/834202f69c99a331ffcc839081ed57]
May-27 15:22:27.053 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:22:27.053 [Task submitter] INFO  nextflow.Session - [c0/ef7996] Submitted process > PROCESS_HITS (batch_45)
May-27 15:22:27.228 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 108; name: PROCESS_HITS (batch_49); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/41/0fb8565703c894662370c25b606438]
May-27 15:22:27.247 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 107; name: PROCESS_HITS (batch_65); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/70/ae6b5e5a4ddd7432408a2a6da32e8d]
May-27 15:22:27.255 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:22:27.256 [Task submitter] INFO  nextflow.Session - [06/16cf40] Submitted process > PROCESS_HITS (batch_58)
May-27 15:22:27.284 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:22:27.285 [Task submitter] INFO  nextflow.Session - [bf/f3717b] Submitted process > PROCESS_HITS (batch_56)
May-27 15:22:27.493 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 106; name: PROCESS_HITS (batch_46); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e9/54f5a81c2b34d6230432a88c57212b]
May-27 15:22:27.529 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:22:27.529 [Task submitter] INFO  nextflow.Session - [f1/f65ef2] Submitted process > PROCESS_HITS (batch_59)
May-27 15:22:47.743 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 109; name: PROCESS_HITS (batch_36); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/6e/dbea0630dda1ba06c92ce25cb9c7c1]
May-27 15:22:47.770 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:22:47.770 [Task submitter] INFO  nextflow.Session - [21/014970] Submitted process > PROCESS_HITS (batch_35)
May-27 15:22:48.483 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 110; name: PROCESS_HITS (batch_47); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/12/2430154c7b82fd0937474b4b2f1e41]
May-27 15:22:48.502 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 112; name: PROCESS_HITS (batch_61); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/dc/bd2e0e4510dd6d38d1a36eaec9406b]
May-27 15:22:48.504 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:22:48.504 [Task submitter] INFO  nextflow.Session - [73/4bdbf2] Submitted process > PROCESS_HITS (batch_37)
May-27 15:22:48.536 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:22:48.537 [Task submitter] INFO  nextflow.Session - [f5/5b3d2c] Submitted process > PROCESS_HITS (batch_52)
May-27 15:22:49.055 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 113; name: PROCESS_HITS (batch_45); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c0/ef79965e043691d585e12a63b0ecef]
May-27 15:22:49.079 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:22:49.079 [Task submitter] INFO  nextflow.Session - [2a/a206db] Submitted process > PROCESS_HITS (batch_40)
May-27 15:22:49.111 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 111; name: PROCESS_HITS (batch_38); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/cd/b3c41ab39c124d448aac4ef10b3606]
May-27 15:22:49.134 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:22:49.134 [Task submitter] INFO  nextflow.Session - [dc/01a7b3] Submitted process > PROCESS_HITS (batch_62)
May-27 15:22:49.229 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 114; name: PROCESS_HITS (batch_58); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/06/16cf40b9ab3d1d896d6836221590a7]
May-27 15:22:49.254 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:22:49.255 [Task submitter] INFO  nextflow.Session - [b3/7ddcb8] Submitted process > PROCESS_HITS (batch_66)
May-27 15:22:49.314 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 115; name: PROCESS_HITS (batch_56); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/bf/f3717b830e6171be66b0cfe96e5cf2]
May-27 15:22:49.387 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:22:49.387 [Task submitter] INFO  nextflow.Session - [3b/5252ee] Submitted process > PROCESS_HITS (batch_43)
May-27 15:22:49.712 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 116; name: PROCESS_HITS (batch_59); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f1/f65ef24cdf004b23c1990969abfc4d]
May-27 15:22:49.740 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:22:49.740 [Task submitter] INFO  nextflow.Session - [15/8782d2] Submitted process > PROCESS_HITS (batch_42)
May-27 15:22:51.745 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 122; name: PROCESS_HITS (batch_66); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/b3/7ddcb8c623acdc97fffbcd99c1447f]
May-27 15:22:51.768 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:22:51.769 [Task submitter] INFO  nextflow.Session - [66/ab8730] Submitted process > PROCESS_HITS (batch_50)
May-27 15:23:11.339 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 117; name: PROCESS_HITS (batch_35); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/21/014970352ba921ce06f7f13fc55196]
May-27 15:23:11.376 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:23:11.377 [Task submitter] INFO  nextflow.Session - [f6/da5f09] Submitted process > PROCESS_HITS (batch_57)
May-27 15:23:12.127 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 118; name: PROCESS_HITS (batch_37); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/73/4bdbf2f44db4c6650374bc3425bce8]
May-27 15:23:12.143 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:23:12.144 [Task submitter] INFO  nextflow.Session - [89/97d5ee] Submitted process > PROCESS_HITS (batch_48)
May-27 15:23:12.312 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 119; name: PROCESS_HITS (batch_52); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f5/5b3d2c8af38bba2f95fb96702f38bb]
May-27 15:23:12.345 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:23:12.346 [Task submitter] INFO  nextflow.Session - [94/046b37] Submitted process > PROCESS_HITS (batch_54)
May-27 15:23:12.779 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 123; name: PROCESS_HITS (batch_43); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/3b/5252ee43887d6b1bbb0ce3f3535acb]
May-27 15:23:12.800 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:23:12.801 [Task submitter] INFO  nextflow.Session - [25/8b4a5c] Submitted process > PROCESS_HITS (batch_33)
May-27 15:23:12.815 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 120; name: PROCESS_HITS (batch_40); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/2a/a206dbe0a83a80d3d0eed3dc158958]
May-27 15:23:12.833 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:23:12.833 [Task submitter] INFO  nextflow.Session - [c4/158cb9] Submitted process > PROCESS_HITS (batch_60)
May-27 15:23:13.077 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 121; name: PROCESS_HITS (batch_62); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/dc/01a7b38135677dcf64b2cbb331628c]
May-27 15:23:13.103 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:23:13.103 [Task submitter] INFO  nextflow.Session - [c9/b52d49] Submitted process > PROCESS_HITS (batch_41)
May-27 15:23:13.744 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 124; name: PROCESS_HITS (batch_42); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/15/8782d217a26c8004e0ebb4972fdf9f]
May-27 15:23:13.768 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:23:13.769 [Task submitter] INFO  nextflow.Session - [4a/46c4d9] Submitted process > PROCESS_HITS (batch_55)
May-27 15:23:15.335 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 125; name: PROCESS_HITS (batch_50); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/66/ab87302f3895b70e01767532ad6de8]
May-27 15:23:15.356 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:23:15.356 [Task submitter] INFO  nextflow.Session - [db/35fec8] Submitted process > PROCESS_HITS (batch_39)
May-27 15:23:31.937 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 126; name: PROCESS_HITS (batch_57); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/f6/da5f09cb671f2002331afa20808c4f]
May-27 15:23:31.972 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:23:31.973 [Task submitter] INFO  nextflow.Session - [69/61b3c2] Submitted process > PROCESS_HITS (batch_63)
May-27 15:23:32.530 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 127; name: PROCESS_HITS (batch_48); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/89/97d5ee4369bb9f52ae2567dac9936f]
May-27 15:23:32.829 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 129; name: PROCESS_HITS (batch_33); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/25/8b4a5c7f63f65cae8010101cf599be]
May-27 15:23:32.951 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 130; name: PROCESS_HITS (batch_60); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c4/158cb956e157d4740840d4567a20cd]
May-27 15:23:33.027 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 128; name: PROCESS_HITS (batch_54); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/94/046b373dbce94bc0efeb472c13145c]
May-27 15:23:33.305 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 131; name: PROCESS_HITS (batch_41); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/c9/b52d49510266fdc4dbcdada22281f9]
May-27 15:23:33.861 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 132; name: PROCESS_HITS (batch_55); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/4a/46c4d9ed57fd2261d50f6aa0e0554f]
May-27 15:23:35.577 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 133; name: PROCESS_HITS (batch_39); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/db/35fec84226f2b25e458e22eefe0a3a]
May-27 15:23:51.259 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 134; name: PROCESS_HITS (batch_63); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/69/61b3c2492f4306af23583a53581797]
May-27 15:26:08.357 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 1 -- submitted tasks are shown below
~> TaskHandler[id: 2; name: GTDBTK (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/7f/b75a841242c48c0225644649a081bb]
May-27 15:28:39.751 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 2; name: GTDBTK (1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/7f/b75a841242c48c0225644649a081bb]
May-27 15:28:39.798 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:28:39.798 [Task submitter] INFO  nextflow.Session - [e5/e9add5] Submitted process > CREATE_DATAFRAME (1)
May-27 15:31:08.451 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 1 -- submitted tasks are shown below
~> TaskHandler[id: 135; name: CREATE_DATAFRAME (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e5/e9add56c4762b58022b371b25a9ba6]
May-27 15:36:08.495 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 1 -- submitted tasks are shown below
~> TaskHandler[id: 135; name: CREATE_DATAFRAME (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e5/e9add56c4762b58022b371b25a9ba6]
May-27 15:41:08.545 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 1 -- submitted tasks are shown below
~> TaskHandler[id: 135; name: CREATE_DATAFRAME (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e5/e9add56c4762b58022b371b25a9ba6]
May-27 15:46:08.588 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 1 -- submitted tasks are shown below
~> TaskHandler[id: 135; name: CREATE_DATAFRAME (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e5/e9add56c4762b58022b371b25a9ba6]
May-27 15:51:08.630 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 1 -- submitted tasks are shown below
~> TaskHandler[id: 135; name: CREATE_DATAFRAME (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e5/e9add56c4762b58022b371b25a9ba6]
May-27 15:56:08.685 [Task monitor] DEBUG n.processor.TaskPollingMonitor - !! executor local > tasks to be completed: 1 -- submitted tasks are shown below
~> TaskHandler[id: 135; name: CREATE_DATAFRAME (1); status: RUNNING; exit: -; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e5/e9add56c4762b58022b371b25a9ba6]
May-27 15:59:02.255 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 135; name: CREATE_DATAFRAME (1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/e5/e9add56c4762b58022b371b25a9ba6]
May-27 15:59:02.302 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 15:59:02.302 [Task submitter] INFO  nextflow.Session - [6e/83a41a] Submitted process > AUTOENCODER (1)
May-27 16:00:14.398 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 136; name: AUTOENCODER (1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/6e/83a41ad28fd7d8ae5ac299687104e0]
May-27 16:00:14.442 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-27 16:00:14.443 [Task submitter] INFO  nextflow.Session - [26/2c938f] Submitted process > MODEL_PREDICT (1)
May-27 16:00:18.200 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 137; name: MODEL_PREDICT (1); status: COMPLETED; exit: 0; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/26/2c938fea0a9b445264b284a2d2f166]
May-27 16:00:18.204 [main] DEBUG nextflow.Session - Session await > all processes finished
May-27 16:00:18.300 [Task monitor] DEBUG n.processor.TaskPollingMonitor - <<< barrier arrives (monitor: local) - terminating tasks monitor poll loop
May-27 16:00:18.300 [main] DEBUG nextflow.Session - Session await > all barriers passed
May-27 16:00:18.305 [main] DEBUG nextflow.util.ThreadPoolManager - Thread pool 'TaskFinalizer' shutdown completed (hard=false)
May-27 16:00:18.309 [main] DEBUG nextflow.util.ThreadPoolManager - Thread pool 'PublishDir' shutdown completed (hard=false)
May-27 16:00:18.314 [main] DEBUG n.trace.WorkflowStatsObserver - Workflow completed > WorkflowStats[succeededCount=137; failedCount=0; ignoredCount=0; cachedCount=0; pendingCount=0; submittedCount=0; runningCount=0; retriesCount=0; abortedCount=0; succeedDuration=9h 58m 28s; failedDuration=0ms; cachedDuration=0ms;loadCpus=0; loadMemory=0; peakRunning=12; peakCpus=38; peakMemory=76 GB; ]
May-27 16:00:18.537 [main] DEBUG nextflow.cache.CacheDB - Closing CacheDB done
May-27 16:00:18.573 [main] DEBUG nextflow.util.ThreadPoolManager - Thread pool 'FileTransfer' shutdown completed (hard=false)
May-27 16:00:18.573 [main] DEBUG nextflow.script.ScriptRunner - > Execution complete -- Goodbye
