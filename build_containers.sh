#!/bin/bash

# Script to build Singularity containers for the genomic data processing workflow

# Create directory for containers if it doesn't exist
mkdir -p containers

# Build Prodigal container
echo "Building Prodigal container..."
singularity build --fakeroot containers/prodigal.sif containers/Prodigal.def

# Build GTDB-Tk container
echo "Building GTDB-Tk container..."
singularity build --fakeroot containers/gtdbtk.sif containers/GTDBTK.def

# Build HMMER container
echo "Building HMMER container..."
singularity build --fakeroot containers/hmmer.sif containers/HMMER.def

# Python is included in each tool container, so no separate Python container is needed

echo "All containers built successfully!"
