#!/bin/bash

# This script fixes the CREATE_DATAFRAME process in the Nextflow workflow
# by manually copying the processed hits to the expected location

# Create the directory structure
mkdir -p csv_hits
for i in $(seq 1 66); do
    mkdir -p csv_hits/batch_$i
done

# Copy files from results_GCF_025823245/processed_hits directly
echo "Copying files from results_GCF_025823245/processed_hits"
for i in $(seq 1 66); do
    echo "Processing batch $i"
    # Use find to locate all CSV files in the results directory for this batch
    find results_GCF_025823245/processed_hits/batch_$i -name "*.csv" -exec cp {} csv_hits/batch_$i/ \; 2>/dev/null || echo "No files found for batch $i"
done

# Debug: List the contents of the csv_hits directory
echo "Contents of csv_hits directory:"
find csv_hits -type f | wc -l

# Run the script with the organized directory structure
python create_unique_df_hits_optimized.py csv_hits results_GCF_025823245/gtdbtk/gtdb_classification.csv final_results.csv

# Copy the result to the output directory
cp final_results.csv results_GCF_025823245/

# Now run the autoencoder
python autoencoder_tool.py encode results_GCF_025823245/final_results.csv robustscaler_enc1024_layers1.pkl robustscaler_enc1024_layers1.h5 encoded_features.csv
cp encoded_features.csv results_GCF_025823245/

# Finally, run the prediction
python model_load_and_predict.py --predict-only --model-path best_refined_model_RS.pkl --sample-path results_GCF_025823245/encoded_features.csv --output-path prediction_results.csv
cp prediction_results.csv results_GCF_025823245/

echo "Workflow completed successfully!"
