May-28 10:26:04.544 [main] DEBUG nextflow.cli.Launcher - $> nextflow run main.nf --genome GCF_008369605.1.fna --skip_tax
May-28 10:26:04.667 [main] DEBUG nextflow.cli.CmdRun - N E X T F L O W  ~  version 25.04.2
May-28 10:26:04.701 [main] DEBUG nextflow.plugin.PluginsFacade - Setting up plugin manager > mode=prod; embedded=false; plugins-dir=/clusterfs/jgi/groups/science/homes/laureli/.nextflow/plugins; core-plugins: nf-amazon@2.15.0,nf-azure@1.16.0,nf-cloudcache@0.4.3,nf-codecommit@0.2.3,nf-console@1.2.1,nf-google@1.21.0,nf-k8s@1.0.0,nf-tower@1.11.2,nf-wave@1.12.1
May-28 10:26:04.758 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Enabled plugins: []
May-28 10:26:04.760 [main] INFO  o.pf4j.DefaultPluginStatusProvider - Disabled plugins: []
May-28 10:26:04.763 [main] INFO  org.pf4j.DefaultPluginManager - PF4J version 3.12.0 in 'deployment' mode
May-28 10:26:04.784 [main] INFO  org.pf4j.AbstractPluginManager - No plugins
May-28 10:26:04.803 [main] DEBUG nextflow.config.ConfigBuilder - Found config local: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/nextflow.config
May-28 10:26:04.817 [main] DEBUG nextflow.config.ConfigBuilder - Parsing config file: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/nextflow.config
May-28 10:26:04.854 [main] DEBUG n.secret.LocalSecretsProvider - Secrets store: /clusterfs/jgi/groups/science/homes/laureli/.nextflow/secrets/store.json
May-28 10:26:04.857 [main] DEBUG nextflow.secret.SecretsLoader - Discovered secrets providers: [nextflow.secret.LocalSecretsProvider@5b5caf08] - activable => nextflow.secret.LocalSecretsProvider@5b5caf08
May-28 10:26:04.869 [main] DEBUG nextflow.config.ConfigBuilder - Applying config profile: `standard`
May-28 10:26:05.428 [main] DEBUG nextflow.cli.CmdRun - Applied DSL=2 from script declaration
May-28 10:26:05.451 [main] DEBUG nextflow.cli.CmdRun - Launching `main.nf` [gloomy_ride] DSL2 - revision: 572cf1876f
May-28 10:26:05.452 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins default=[]
May-28 10:26:05.462 [main] DEBUG nextflow.plugin.PluginsFacade - Plugins resolved requirement=[]
May-28 10:26:05.522 [main] DEBUG nextflow.Session - Session UUID: aca062c9-cd17-498b-9d55-7854bfa9e9b5
May-28 10:26:05.523 [main] DEBUG nextflow.Session - Run name: gloomy_ride
May-28 10:26:05.535 [main] DEBUG nextflow.Session - Executor pool size: 32
May-28 10:26:05.546 [main] DEBUG nextflow.file.FilePorter - File porter settings maxRetries=3; maxTransfers=50; pollTimeout=null
May-28 10:26:05.550 [main] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'FileTransfer' minSize=10; maxSize=96; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
May-28 10:26:05.627 [main] DEBUG nextflow.cli.CmdRun - 
  Version: 25.04.2 build 5947
  Created: 13-05-2025 21:58 UTC (14:58 PDT)
  System: Linux 4.18.0-553.5.1.el8_10.x86_64
  Runtime: Groovy 4.0.26 on OpenJDK 64-Bit Server VM 17.0.11-internal+0-adhoc..src
  Encoding: UTF-8 (UTF-8)
  Process: <EMAIL> [10.0.7.9]
  CPUs: 32 - Mem: 503.8 GB (7.2 GB) - Swap: 8 GB (32 KB)
May-28 10:26:05.715 [main] DEBUG nextflow.Session - Work-dir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work [nfs]
May-28 10:26:05.756 [main] DEBUG nextflow.executor.ExecutorFactory - Extension executors providers=[]
May-28 10:26:05.767 [main] DEBUG nextflow.Session - Observer factory: DefaultObserverFactory
May-28 10:26:05.772 [main] DEBUG nextflow.Session - Observer factory (v2): LinObserverFactory
May-28 10:26:05.798 [main] DEBUG nextflow.cache.CacheFactory - Using Nextflow cache factory: nextflow.cache.DefaultCacheFactory
May-28 10:26:05.808 [main] DEBUG nextflow.util.CustomThreadPool - Creating default thread pool > poolSize: 33; maxThreads: 1000
May-28 10:26:06.083 [main] DEBUG nextflow.Session - Session start
May-28 10:26:06.763 [main] DEBUG nextflow.script.ScriptRunner - > Launching execution
May-28 10:26:06.798 [main] INFO  nextflow.Nextflow - 
==============================================
 GENOMIC DATA PROCESSING WORKFLOW
==============================================
 input genome: GCF_008369605.1.fna
 outdir     : /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/results
 gtdbtk_db  : /clusterfs/jgi/scratch/science/mgs/nelli/databases/gtdbtk/release220
 hmm_base_dir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/1_pangenomes_features/genera_pangenome_est_30_Sept/hmm_est_5_30_Sept
 processing batches: 1 to 66
 scaler path: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/robustscaler_enc1024_layers1.pkl
 model path : /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/robustscaler_enc1024_layers1.h5
 autoencoder output: encoded_features.csv
 ML model path: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/best_refined_model_RS.pkl
 prediction output: prediction_results.csv
 prediction version: 1
 skip taxonomic classification: true
==============================================

May-28 10:26:07.127 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:PRODIGAL` matches process PRODIGAL
May-28 10:26:07.136 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-28 10:26:07.137 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-28 10:26:07.148 [main] DEBUG nextflow.executor.Executor - [warm up] executor > local
May-28 10:26:07.154 [main] DEBUG n.processor.LocalPollingMonitor - Creating local task monitor for executor 'local' > cpus=32; memory=503.8 GB; capacity=32; pollInterval=100ms; dumpInterval=5m
May-28 10:26:07.161 [main] DEBUG n.processor.TaskPollingMonitor - >>> barrier register (monitor: local)
May-28 10:26:07.180 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'PRODIGAL': maxForks=0; fair=false; array=0
May-28 10:26:07.231 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:HMMSEARCH` matches process HMMSEARCH
May-28 10:26:07.247 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-28 10:26:07.247 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-28 10:26:07.248 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'HMMSEARCH': maxForks=0; fair=false; array=0
May-28 10:26:07.251 [main] DEBUG nextflow.processor.TaskProcessor - Creating *combiner* operator for each param(s) at index(es): [0, 1, 2]
May-28 10:26:07.266 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:PROCESS_HITS` matches process PROCESS_HITS
May-28 10:26:07.268 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-28 10:26:07.268 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-28 10:26:07.279 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'PROCESS_HITS': maxForks=0; fair=false; array=0
May-28 10:26:07.281 [main] DEBUG nextflow.processor.TaskProcessor - Creating *combiner* operator for each param(s) at index(es): [1]
May-28 10:26:07.290 [main] DEBUG nextflow.script.ProcessConfig - Config settings `withName:CREATE_DATAFRAME` matches process CREATE_DATAFRAME
May-28 10:26:07.291 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-28 10:26:07.296 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-28 10:26:07.302 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'CREATE_DATAFRAME': maxForks=0; fair=false; array=0
May-28 10:26:07.331 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-28 10:26:07.331 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-28 10:26:07.332 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'AUTOENCODER': maxForks=0; fair=false; array=0
May-28 10:26:07.346 [main] DEBUG nextflow.executor.ExecutorFactory - << taskConfig executor: local
May-28 10:26:07.346 [main] DEBUG nextflow.executor.ExecutorFactory - >> processorType: 'local'
May-28 10:26:07.356 [main] DEBUG nextflow.processor.TaskProcessor - Creating process 'MODEL_PREDICT': maxForks=0; fair=false; array=0
May-28 10:26:07.360 [main] DEBUG nextflow.Session - Workflow process names [dsl2]: CREATE_DATAFRAME, MODEL_PREDICT, GTDBTK, HMMSEARCH, PRODIGAL, PROCESS_HITS, AUTOENCODER
May-28 10:26:07.367 [main] DEBUG nextflow.Session - Igniting dataflow network (22)
May-28 10:26:07.378 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > PRODIGAL
May-28 10:26:07.379 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > HMMSEARCH
May-28 10:26:07.389 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > HMMSEARCH
May-28 10:26:07.391 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > PROCESS_HITS
May-28 10:26:07.404 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > PROCESS_HITS
May-28 10:26:07.413 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > CREATE_DATAFRAME
May-28 10:26:07.415 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > AUTOENCODER
May-28 10:26:07.417 [main] DEBUG nextflow.processor.TaskProcessor - Starting process > MODEL_PREDICT
May-28 10:26:07.421 [main] DEBUG nextflow.script.ScriptRunner - Parsed script files:
  Script_f5dec23c1b3efd54: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/main.nf
May-28 10:26:07.422 [main] DEBUG nextflow.script.ScriptRunner - > Awaiting termination 
May-28 10:26:07.427 [main] DEBUG nextflow.Session - Session await
May-28 10:26:07.566 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 10:26:07.570 [Task submitter] INFO  nextflow.Session - [77/805b69] Submitted process > PRODIGAL (1)
May-28 10:26:07.605 [Task submitter] DEBUG n.executor.local.LocalTaskHandler - Launch cmd line: /bin/bash -ue .command.run
May-28 10:26:07.605 [Task submitter] INFO  nextflow.Session - [72/687385] Submitted process > CREATE_DATAFRAME (1)
May-28 10:26:08.894 [Task monitor] DEBUG n.processor.TaskPollingMonitor - Task completed > TaskHandler[id: 2; name: CREATE_DATAFRAME (1); status: COMPLETED; exit: 1; error: -; workDir: /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/72/6873856ee7164f31341750ca8ca363]
May-28 10:26:08.895 [Task monitor] DEBUG nextflow.util.ThreadPoolBuilder - Creating thread pool 'TaskFinalizer' minSize=10; maxSize=96; workQueue=LinkedBlockingQueue[-1]; allowCoreThreadTimeout=false
May-28 10:26:08.910 [TaskFinalizer-1] DEBUG nextflow.processor.TaskProcessor - Handling unexpected condition for
  task: name=CREATE_DATAFRAME (1); work-dir=/clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/72/6873856ee7164f31341750ca8ca363
  error [nextflow.exception.ProcessFailedException]: Process `CREATE_DATAFRAME (1)` terminated with an error exit status (1)
May-28 10:26:08.933 [TaskFinalizer-1] ERROR nextflow.processor.TaskProcessor - Error executing process > 'CREATE_DATAFRAME (1)'

Caused by:
  Process `CREATE_DATAFRAME (1)` terminated with an error exit status (1)


Command executed:

  # Create directory structure expected by the script
  mkdir -p csv_hits
  for i in $(seq 1 66); do
      mkdir -p csv_hits/batch_$i
  done
  
  # Debug: List all CSV files
  echo "All CSV files to process:"
  ls -la main.nf || echo "No CSV files found"
  
  # Get the project root directory (where main.nf is located)
  PROJECT_ROOT=$(dirname $(readlink -f main.nf))
  echo "Project root: $PROJECT_ROOT"
  
  # Get the output directory path
  OUTDIR="$PROJECT_ROOT/results"
  echo "Output directory: $OUTDIR"
  
  # Copy files from the output directory's processed_hits directly
  echo "Copying files from $OUTDIR/processed_hits"
  
  # Check if the directory exists
  if [ ! -d "$OUTDIR/processed_hits" ]; then
      echo "Directory $OUTDIR/processed_hits does not exist"
      echo "Current directory: $(pwd)"
      echo "Listing current directory:"
      ls -la
      echo "Listing project root:"
      ls -la $PROJECT_ROOT
      exit 1
  fi
  
  # Find all batch directories in the processed_hits directory
  BATCH_DIRS=$(find $OUTDIR/processed_hits -maxdepth 1 -type d -name "batch_*" | sort)
  
  if [ -z "$BATCH_DIRS" ]; then
      echo "No batch directories found in $OUTDIR/processed_hits"
      exit 1
  fi
  
  # Process each batch directory
  for BATCH_DIR in $BATCH_DIRS; do
      BATCH_NAME=$(basename $BATCH_DIR)
      echo "Processing $BATCH_NAME"
  
      # Extract batch number
      BATCH_NUM=$(echo $BATCH_NAME | sed 's/batch_//')
  
      # Create the corresponding directory in csv_hits if it doesn't exist
      mkdir -p csv_hits/$BATCH_NAME
  
      # Copy CSV files using absolute paths
      find "$OUTDIR/processed_hits/$BATCH_NAME" -name "*.csv" -exec cp {} csv_hits/$BATCH_NAME/ \;
  done
  
  # Debug: List the contents of the csv_hits directory
  echo "Contents of csv_hits directory:"
  find csv_hits -type f | sort
  
  # Run the script with the organized directory structure
  if [ "true" = "true" ]; then
      echo "Running without taxonomic classification (--skip_tax enabled)"
      python create_unique_df_hits_optimized.py csv_hits --skip_tax final_results.csv
  else
      echo "Running with taxonomic classification"
      python create_unique_df_hits_optimized.py csv_hits nextflow.config final_results.csv
  fi

Command exit status:
  1

Command output:
  -rw-rw-r-- 1 <USER> <GROUP>  761 May 28 10:26 .command.out
  -rw-rw-r-- 1 <USER> <GROUP> 4579 May 28 10:26 .command.run
  -rw-rw-r-- 1 <USER> <GROUP> 2139 May 28 10:26 .command.sh
  lrwxrwxrwx 1 laureli nogroup  114 May 28 10:26 create_unique_df_hits_optimized.py -> /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/bin/create_unique_df_hits_optimized.py
  drwxrwsr-x 2 laureli nogroup 4096 May 28 10:26 csv_hits
  lrwxrwxrwx 1 laureli nogroup   83 May 28 10:26 main.nf -> /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/main.nf
  lrwxrwxrwx 1 laureli nogroup   91 May 28 10:26 nextflow.config -> /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/nextflow.config
  Listing project root:
  total 9484604
  drwxrwsr-x 2 laureli nogroup       4096 May 28 10:26 .
  drwxrwsr-x 2 laureli nogroup       4096 May 19 05:29 ..
  drwxrwsr-x 2 laureli nogroup       4096 May 28 10:26 .nextflow
  -rw-rw-r-- 1 <USER> <GROUP>      10068 May 28 10:26 .nextflow.log
  -rw-rw-r-- 1 <USER> <GROUP>      95895 May 27 16:00 .nextflow.log.1
  -rw-rw-r-- 1 <USER> <GROUP>    4190374 May 19 05:48 GCF_008369605.1.fna
  -rw-rw-r-- 1 <USER> <GROUP>    4809616 Oct 26  2022 GCF_025823245.1.fna
  -rw-rw-r-- 1 <USER> <GROUP>       4872 May 19 22:26 README.md
  -rw-rw-r-- 1 <USER> <GROUP>      12855 May 20 12:42 autoencoder_tool.py
  drwxrwsr-x 2 laureli nogroup       4096 May 19 09:22 backup
  drwxrwsr-x 2 laureli nogroup       4096 May 22 04:29 backup_parallel
  -rw-rw-r-- 1 <USER> <GROUP>     487562 May 20 13:56 best_refined_model_RS.pkl
  drwxrwsr-x 2 laureli nogroup       4096 May 19 07:56 bin
  -rwxrwxr-x 1 <USER> <GROUP>        712 May 19 07:56 build_containers.sh
  -rw-rw-r-- 1 <USER> <GROUP>       5569 May 20 12:19 check_compatibility.py
  drwxrwsr-x 2 laureli nogroup       4096 May 19 08:55 containers
  -rw-rw-r-- 1 <USER> <GROUP>       8199 May 19 05:37 convert_hits.py
  -rw-rw-r-- 1 <USER> <GROUP>       9583 May 19 05:31 create_unique_df_hits_optimized.py
  -rw-rw-r-- 1 <USER> <GROUP>       5846 May 20 12:19 encode_final_results.py
  -rwxrwxr-x 1 <USER> <GROUP>       1612 May 21 02:28 fix_workflow.sh
  -rwxrwxr-x 1 <USER> <GROUP>       4552 May 19 05:30 hmmsearch_python.py
  -rw-rw-r-- 1 <USER> <GROUP>      12303 May 27 11:26 main.nf
  -rw-rw-r-- 1 <USER> <GROUP>      10864 May 21 09:56 main.nf.fixed
  drwxrwsr-x 2 laureli nogroup       4096 May 27 08:23 ml
  -rw-rw-r-- 1 <USER> <GROUP>       9532 May 20 13:56 model_load_and_predict.py
  -rw-rw-r-- 1 <USER> <GROUP>       1922 May 27 10:42 nextflow.config
  drwxrwsr-x 2 laureli nogroup       4096 May 22 05:06 ppred
  -rwxrwxr-x 1 <USER> <GROUP>       1741 May 20 10:39 process_existing_results.sh
  -rw-rw-r-- 1 <USER> <GROUP>       2739 May 20 10:31 process_results.py
  -rw-rw-r-- 1 <USER> <GROUP>       1231 May 19 07:11 project_summary.txt
  -rw-rw-r-- 1 <USER> <GROUP>         22 May 28 01:52 remove.log
  -rw-rw-r-- 1 <USER> <GROUP>    2944023 May 27 09:15 report-20250527-33328296.html
  drwxrwsr-x 2 laureli nogroup       4096 May 28 10:26 results
  -rw-rw-r-- 1 <USER> <GROUP> 9698149560 May 20 12:44 robustscaler_enc1024_layers1.h5
  -rw-rw-r-- 1 <USER> <GROUP>    1171829 May 20 12:43 robustscaler_enc1024_layers1.pkl
  -rw-rw-r-- 1 <USER> <GROUP>       2720 May 20 10:34 run_final_step.py
  -rwxrwxr-x 1 <USER> <GROUP>      11673 May 19 05:35 run_gtdbtk.py
  -rwxrwxr-x 1 <USER> <GROUP>       6093 May 19 05:29 run_prodigal_and_rename.py
  -rw-rw-r-- 1 <USER> <GROUP>        261 May 19 23:33 test_path.nf
  -rw-rw-r-- 1 <USER> <GROUP>     252307 May 27 09:15 timeline-20250527-33328296.html
  drwxrwsr-x 2 laureli nogroup       4096 May 28 10:26 work

Command error:
  -rw-rw-r-- 1 <USER> <GROUP>  761 May 28 10:26 .command.out
  -rw-rw-r-- 1 <USER> <GROUP> 4579 May 28 10:26 .command.run
  -rw-rw-r-- 1 <USER> <GROUP> 2139 May 28 10:26 .command.sh
  lrwxrwxrwx 1 laureli nogroup  114 May 28 10:26 create_unique_df_hits_optimized.py -> /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/bin/create_unique_df_hits_optimized.py
  drwxrwsr-x 2 laureli nogroup 4096 May 28 10:26 csv_hits
  lrwxrwxrwx 1 laureli nogroup   83 May 28 10:26 main.nf -> /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/main.nf
  lrwxrwxrwx 1 laureli nogroup   91 May 28 10:26 nextflow.config -> /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/nextflow.config
  Listing project root:
  total 9484604
  drwxrwsr-x 2 laureli nogroup       4096 May 28 10:26 .
  drwxrwsr-x 2 laureli nogroup       4096 May 19 05:29 ..
  drwxrwsr-x 2 laureli nogroup       4096 May 28 10:26 .nextflow
  -rw-rw-r-- 1 <USER> <GROUP>      10068 May 28 10:26 .nextflow.log
  -rw-rw-r-- 1 <USER> <GROUP>      95895 May 27 16:00 .nextflow.log.1
  -rw-rw-r-- 1 <USER> <GROUP>    4190374 May 19 05:48 GCF_008369605.1.fna
  -rw-rw-r-- 1 <USER> <GROUP>    4809616 Oct 26  2022 GCF_025823245.1.fna
  -rw-rw-r-- 1 <USER> <GROUP>       4872 May 19 22:26 README.md
  -rw-rw-r-- 1 <USER> <GROUP>      12855 May 20 12:42 autoencoder_tool.py
  drwxrwsr-x 2 laureli nogroup       4096 May 19 09:22 backup
  drwxrwsr-x 2 laureli nogroup       4096 May 22 04:29 backup_parallel
  -rw-rw-r-- 1 <USER> <GROUP>     487562 May 20 13:56 best_refined_model_RS.pkl
  drwxrwsr-x 2 laureli nogroup       4096 May 19 07:56 bin
  -rwxrwxr-x 1 <USER> <GROUP>        712 May 19 07:56 build_containers.sh
  -rw-rw-r-- 1 <USER> <GROUP>       5569 May 20 12:19 check_compatibility.py
  drwxrwsr-x 2 laureli nogroup       4096 May 19 08:55 containers
  -rw-rw-r-- 1 <USER> <GROUP>       8199 May 19 05:37 convert_hits.py
  -rw-rw-r-- 1 <USER> <GROUP>       9583 May 19 05:31 create_unique_df_hits_optimized.py
  -rw-rw-r-- 1 <USER> <GROUP>       5846 May 20 12:19 encode_final_results.py
  -rwxrwxr-x 1 <USER> <GROUP>       1612 May 21 02:28 fix_workflow.sh
  -rwxrwxr-x 1 <USER> <GROUP>       4552 May 19 05:30 hmmsearch_python.py
  -rw-rw-r-- 1 <USER> <GROUP>      12303 May 27 11:26 main.nf
  -rw-rw-r-- 1 <USER> <GROUP>      10864 May 21 09:56 main.nf.fixed
  drwxrwsr-x 2 laureli nogroup       4096 May 27 08:23 ml
  -rw-rw-r-- 1 <USER> <GROUP>       9532 May 20 13:56 model_load_and_predict.py
  -rw-rw-r-- 1 <USER> <GROUP>       1922 May 27 10:42 nextflow.config
  drwxrwsr-x 2 laureli nogroup       4096 May 22 05:06 ppred
  -rwxrwxr-x 1 <USER> <GROUP>       1741 May 20 10:39 process_existing_results.sh
  -rw-rw-r-- 1 <USER> <GROUP>       2739 May 20 10:31 process_results.py
  -rw-rw-r-- 1 <USER> <GROUP>       1231 May 19 07:11 project_summary.txt
  -rw-rw-r-- 1 <USER> <GROUP>         22 May 28 01:52 remove.log
  -rw-rw-r-- 1 <USER> <GROUP>    2944023 May 27 09:15 report-20250527-33328296.html
  drwxrwsr-x 2 laureli nogroup       4096 May 28 10:26 results
  -rw-rw-r-- 1 <USER> <GROUP> 9698149560 May 20 12:44 robustscaler_enc1024_layers1.h5
  -rw-rw-r-- 1 <USER> <GROUP>    1171829 May 20 12:43 robustscaler_enc1024_layers1.pkl
  -rw-rw-r-- 1 <USER> <GROUP>       2720 May 20 10:34 run_final_step.py
  -rwxrwxr-x 1 <USER> <GROUP>      11673 May 19 05:35 run_gtdbtk.py
  -rwxrwxr-x 1 <USER> <GROUP>       6093 May 19 05:29 run_prodigal_and_rename.py
  -rw-rw-r-- 1 <USER> <GROUP>        261 May 19 23:33 test_path.nf
  -rw-rw-r-- 1 <USER> <GROUP>     252307 May 27 09:15 timeline-20250527-33328296.html
  drwxrwsr-x 2 laureli nogroup       4096 May 28 10:26 work

Work dir:
  /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/work/72/6873856ee7164f31341750ca8ca363

Container:
  /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/hmmer_workflow/nf_workflow/containers/hmmer.sif

Tip: when you have fixed the problem you can continue the execution adding the option `-resume` to the run command line
May-28 10:26:08.939 [TaskFinalizer-1] DEBUG nextflow.Session - Session aborted -- Cause: Process `CREATE_DATAFRAME (1)` terminated with an error exit status (1)
May-28 10:26:08.955 [TaskFinalizer-1] DEBUG nextflow.Session - The following nodes are still active:
[process] HMMSEARCH
  status=ACTIVE
  port 0: (value) OPEN  ; channel: __$eachinparam<0>
  port 1: (value) bound ; channel: __$eachinparam<1>
  port 2: (value) bound ; channel: __$eachinparam<2>
  port 3: (cntrl) -     ; channel: $

[process] PROCESS_HITS
  status=ACTIVE
  port 0: (queue) OPEN  ; channel: -
  port 1: (value) bound ; channel: __$eachinparam<1>
  port 2: (cntrl) -     ; channel: $

May-28 10:26:08.959 [main] DEBUG nextflow.Session - Session await > all processes finished
May-28 10:26:08.959 [Task monitor] DEBUG n.processor.TaskPollingMonitor - <<< barrier arrives (monitor: local) - terminating tasks monitor poll loop
May-28 10:26:08.962 [main] DEBUG nextflow.Session - Session await > all barriers passed
May-28 10:26:08.971 [main] WARN  n.processor.TaskPollingMonitor - Killing running tasks (1)
May-28 10:26:08.987 [main] DEBUG n.trace.WorkflowStatsObserver - Workflow completed > WorkflowStats[succeededCount=0; failedCount=1; ignoredCount=0; cachedCount=0; pendingCount=0; submittedCount=0; runningCount=0; retriesCount=0; abortedCount=1; succeedDuration=0ms; failedDuration=4.9s; cachedDuration=0ms;loadCpus=0; loadMemory=0; peakRunning=2; peakCpus=5; peakMemory=12 GB; ]
May-28 10:26:09.177 [main] DEBUG nextflow.cache.CacheDB - Closing CacheDB done
May-28 10:26:09.196 [main] DEBUG nextflow.script.ScriptRunner - > Execution complete -- Goodbye
