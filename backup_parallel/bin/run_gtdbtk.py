#!/usr/bin/env python3
"""
Python script to run GTDB-Tk classify workflow.

This script executes the GTDB-Tk classify workflow command with the specified parameters.
It allows you to specify the input directory, output directory, and temporary directory.
If output and temporary directories are not provided, they default to 'output' and 'temp' in the current working directory.
The script also processes the classification results to extract taxonomic information and saves it as a CSV file.

Requirements:
- GTDB-Tk must be installed and available in your PATH or conda environment
- Python 3.6 or higher
- GTDB-Tk reference data must be available (set via GTDBTK_DATA_PATH environment variable or --data_path option)
- pandas library for processing the classification results

Usage:
    1. Activate the conda environment with GTDB-Tk:
       conda activate gtdbtk

    2. Run the script with basic options:
       python run_gtdbtk.py --genome_dir /path/to/genomes

    3. Specify output and temporary directories:
       python run_gtdbtk.py --genome_dir /path/to/genomes --out_dir /path/to/output --tmpdir /path/to/temp

    4. Specify the number of CPUs to use:
       python run_gtdbtk.py --genome_dir /path/to/genomes --cpus 16

    5. Specify the GTDB-Tk reference data path:
       python run_gtdbtk.py --genome_dir /path/to/genomes --data_path /path/to/gtdbtk/data

    6. Specify the output CSV file path:
       python run_gtdbtk.py --genome_dir /path/to/genomes --csv_output /path/to/output.csv

    7. Clean up all outputs except the CSV file:
       python run_gtdbtk.py --genome_dir /path/to/genomes --clean_outputs

Example:
    python run_gtdbtk.py --genome_dir ./genomes --out_dir ./output --tmpdir ./temp --cpus 8 --data_path /path/to/gtdbtk/data --csv_output ./gtdb_classification.csv
"""

import os
import sys
import argparse
import subprocess
import pandas as pd
import shutil
import os.path


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description='Run GTDB-Tk classify workflow.',
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )

    parser.add_argument(
        '--genome_dir',
        required=True,
        help='Directory containing genome files (FASTA format)'
    )

    parser.add_argument(
        '--out_dir',
        default=os.path.join(os.getcwd(), 'output'),
        help='Directory to store GTDB-Tk output files (default: ./output)'
    )

    parser.add_argument(
        '--tmpdir',
        default='/tmp/gtdbtk_temp',
        help='Directory for temporary files (default: /tmp/gtdbtk_temp)'
    )

    parser.add_argument(
        '--cpus',
        type=int,
        default=8,
        help='Number of CPUs to use (default: 8)'
    )

    parser.add_argument(
        '--data_path',
        help='Path to the GTDB-Tk reference data (if not set, uses GTDBTK_DATA_PATH environment variable)'
    )

    parser.add_argument(
        '--csv_output',
        default=os.path.join(os.getcwd(), 'gtdb_classification.csv'),
        help='Path to save the processed classification results as CSV (default: ./gtdb_classification.csv)'
    )

    parser.add_argument(
        '--clean_outputs',
        action='store_true',
        help='Clean up all GTDB-Tk outputs except the CSV file after processing'
    )

    return parser.parse_args()


def check_gtdbtk_available():
    """Check if GTDB-Tk is available in the current environment."""
    try:
        result = subprocess.run(
            ["gtdbtk", "--version"],
            capture_output=True,
            text=True,
            check=False
        )
        if result.returncode != 0:
            print("Error: GTDB-Tk is not available in the current environment.")
            print("Please activate the conda environment with GTDB-Tk installed:")
            print("  conda activate gtdbtk")
            return False
        print(f"Found GTDB-Tk: {result.stdout.strip()}")
        return True
    except FileNotFoundError:
        print("Error: GTDB-Tk command not found.")
        print("Please activate the conda environment with GTDB-Tk installed:")
        print("  conda activate gtdbtk")
        return False


def run_gtdbtk(genome_dir, out_dir, tmpdir, cpus, data_path=None):
    """
    Run GTDB-Tk classify workflow.

    Args:
        genome_dir: Directory containing genome files
        out_dir: Directory to store output files
        tmpdir: Directory for temporary files
        cpus: Number of CPUs to use
        data_path: Path to the GTDB-Tk reference data (optional)

    Returns:
        True if GTDB-Tk ran successfully, False otherwise
    """
    # Validate input directory
    if not os.path.exists(genome_dir):
        print(f"Error: Input directory {genome_dir} does not exist.")
        return False

    # Create output directory if it doesn't exist
    os.makedirs(out_dir, exist_ok=True)
    print(f"Output directory: {out_dir}")

    # Create temporary directory if it doesn't exist
    os.makedirs(tmpdir, exist_ok=True)
    print(f"Temporary directory: {tmpdir}")

    # Set up environment for GTDB-Tk
    env = os.environ.copy()

    # Set GTDBTK_DATA_PATH if data_path is provided
    if data_path:
        if not os.path.exists(data_path):
            print(f"Error: GTDB-Tk data path {data_path} does not exist.")
            return False
        env["GTDBTK_DATA_PATH"] = data_path
        print(f"Using GTDB-Tk data path: {data_path}")
    elif "GTDBTK_DATA_PATH" in env:
        print(f"Using GTDB-Tk data path from environment: {env['GTDBTK_DATA_PATH']}")
    else:
        print("Warning: GTDBTK_DATA_PATH is not set. GTDB-Tk may fail if reference data is not found.")

    # Build the GTDB-Tk command
    cmd = [
        "gtdbtk",
        "classify_wf",
        "--cpus", str(cpus),
        "--genome_dir", genome_dir,
        "--out_dir", out_dir,
        "--tmpdir", tmpdir,
        "--skip_ani_screen"
    ]

    print(f"Running GTDB-Tk command: {' '.join(cmd)}")

    try:
        # Run GTDB-Tk
        subprocess.run(
            cmd,
            check=True,
            text=True,
            env=env
        )
        print("GTDB-Tk completed successfully.")

        # Process the GTDB-Tk output to create a simplified CSV file
        summary_file = os.path.join(out_dir, 'classify', 'gtdbtk.bac120.summary.tsv')
        if os.path.exists(summary_file):
            process_classification_results(out_dir, os.path.join(os.path.dirname(out_dir), 'gtdb_classification.csv'))
            return True
        else:
            print(f"Error: GTDB-Tk summary file not found at {summary_file}")
            return False
    except subprocess.CalledProcessError as e:
        print(f"Error running GTDB-Tk: {e}")
        if "GTDBTK_DATA_PATH" in env:
            print(f"\nThe GTDB-Tk reference data path is set to: {env['GTDBTK_DATA_PATH']}")
            print("Please verify that this path contains valid GTDB-Tk reference data.")
            print("You can download the reference data from: https://data.gtdb.ecogenomic.org/releases/")
        else:
            print("\nThe GTDBTK_DATA_PATH environment variable is not set.")
            print("Please set it to the location of your GTDB-Tk reference data:")
            print("  export GTDBTK_DATA_PATH=/path/to/gtdbtk/data")
            print("Or provide it using the --data_path option.")
        return False


def process_classification_results(out_dir, csv_output):
    """
    Process GTDB-Tk classification results and save as CSV.

    Args:
        out_dir: Directory containing GTDB-Tk output files
        csv_output: Path to save the processed CSV file

    Returns:
        True if processing was successful, False otherwise
    """
    # Path to the classification summary file
    summary_file = os.path.join(out_dir, 'classify', 'gtdbtk.bac120.summary.tsv')

    # Check if the summary file exists
    if not os.path.exists(summary_file):
        print(f"Error: Classification summary file not found: {summary_file}")
        return False

    try:
        # Read the TSV file
        print(f"Processing classification results from: {summary_file}")
        df = pd.read_csv(summary_file, sep='\t')

        # Extract the taxonomic information
        print("Extracting taxonomic information...")
        df['Domain'] = df['classification'].str.extract(r'd__(.*?);p__')
        df['Phylum'] = df['classification'].str.extract(r';p__(.*?);c__')
        df['Class'] = df['classification'].str.extract(r';c__(.*?);o__')
        df['Order'] = df['classification'].str.extract(r';o__(.*?);f__')
        df['Family'] = df['classification'].str.extract(r';f__(.*?);g__')
        df['Genus'] = df['classification'].str.extract(r';g__(.*?);s__')
        df['Species'] = df['classification'].str.extract(r';s__(.*)')

        # Select only the specified columns and reorder them
        df = df[['user_genome', 'Domain', 'Phylum', 'Class', 'Order', 'Family', 'Genus', 'Species']]

        # Save the processed data to CSV
        print(f"Saving processed classification results to: {csv_output}")
        df.to_csv(csv_output, index=False)

        print(f"Classification results processed successfully.")
        return True

    except Exception as e:
        print(f"Error processing classification results: {e}")
        return False


def clean_outputs(out_dir, tmpdir, csv_output):
    """
    Clean up GTDB-Tk output directories, keeping only the CSV file.

    Args:
        out_dir: GTDB-Tk output directory to clean
        tmpdir: GTDB-Tk temporary directory to clean
        csv_output: Path to the CSV file to keep

    Returns:
        True if cleanup was successful, False otherwise
    """
    try:
        # Make sure the CSV file exists before cleaning up
        if not os.path.exists(csv_output):
            print(f"Error: CSV file not found: {csv_output}")
            return False

        print(f"Cleaning up GTDB-Tk outputs...")

        # Remove the temporary directory
        if os.path.exists(tmpdir):
            print(f"Removing temporary directory: {tmpdir}")
            shutil.rmtree(tmpdir)

        # Remove the output directory
        if os.path.exists(out_dir):
            print(f"Removing output directory: {out_dir}")
            shutil.rmtree(out_dir)

        print(f"Cleanup completed. Kept CSV file: {csv_output}")
        return True

    except Exception as e:
        print(f"Error cleaning up outputs: {e}")
        return False


def main():
    """Main function to run GTDB-Tk classify workflow."""
    # Parse command line arguments
    args = parse_arguments()

    # Check if GTDB-Tk is available
    if not check_gtdbtk_available():
        sys.exit(1)

    # Print parameters
    print("\nGTDB-Tk Parameters:")
    print(f"Genome directory: {args.genome_dir}")
    print(f"Output directory: {args.out_dir}")
    print(f"Temporary directory: {args.tmpdir}")
    print(f"CPUs: {args.cpus}")
    print(f"CSV output: {args.csv_output}")
    if args.data_path:
        print(f"GTDB-Tk data path: {args.data_path}")
    if args.clean_outputs:
        print("Clean outputs: Yes")

    # Run GTDB-Tk
    success = run_gtdbtk(
        args.genome_dir,
        args.out_dir,
        args.tmpdir,
        args.cpus,
        args.data_path
    )

    if not success:
        print("\nGTDB-Tk classify workflow failed.")
        sys.exit(1)

    print("\nGTDB-Tk classify workflow completed successfully.")
    print(f"Results are available in: {args.out_dir}")

    # Process classification results
    success = process_classification_results(args.out_dir, args.csv_output)

    if not success:
        print("\nFailed to process classification results.")
        sys.exit(1)

    # Clean up outputs if requested
    if args.clean_outputs:
        success = clean_outputs(args.out_dir, args.tmpdir, args.csv_output)

        if not success:
            print("\nFailed to clean up outputs.")
            sys.exit(1)

    print(f"\nAll tasks completed successfully.")
    print(f"Final classification results are available in: {args.csv_output}")


if __name__ == "__main__":
    main()
