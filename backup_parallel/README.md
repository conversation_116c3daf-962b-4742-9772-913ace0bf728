# Genomic Data Processing Workflow

This is a Nextflow pipeline for processing genomic data with Prodigal, GTDB-Tk, and HMMER, followed by machine learning prediction.

## Overview

This workflow performs the following steps:
1. Runs Prodigal to predict protein-coding genes
2. Runs GTDB-Tk for taxonomic classification
3. Runs HMMSEARCH against HMM profiles
4. Processes HMMSEARCH hits
5. Creates a dataframe with all results
6. Processes the data with an autoencoder
7. Makes predictions using a trained ML model

## Requirements

- Nextflow (>=20.10.0)
- Singularity
- Access to the required databases:
  - GTDB-Tk database
  - HMM profiles

## Usage

```bash
nextflow run main.nf --genome <path_to_genome> --outdir <output_directory>
```

### Parameters

- `--genome`: Path to the input genome (FASTA format)
- `--outdir`: Output directory for results
- `--gtdbtk_db`: Path to GTDB-Tk database
- `--hmm_base_dir`: Path to HMM profiles directory
- `--batch_count`: Number of batches to process (default: 4)
- `--scaler_path`: Path to the saved scaler
- `--model_path`: Path to the saved autoencoder model
- `--ml_model_path`: Path to the ML model for prediction
- `--predict_version`: Version parameter for the MODEL_PREDICT process

## Containers

The workflow uses Singularity containers for:
- Prodigal
- GTDB-Tk
- HMMER

Container definition files are provided in the `containers` directory.

## Scripts

- `bin/run_prodigal_and_rename.py`: Runs Prodigal for gene prediction
- `bin/run_gtdbtk.py`: Runs GTDB-Tk for taxonomic classification
- `bin/hmmsearch_python.py`: Runs HMMSEARCH against HMM profiles
- `bin/convert_hits.py`: Processes HMMSEARCH hits
- `bin/create_unique_df_hits_optimized.py`: Creates a dataframe with all results
- `autoencoder_tool.py`: Processes data with an autoencoder
- `model_load_and_predict.py`: Makes predictions using a trained ML model

## Models

- `robustscaler_enc1024_layers1.pkl`: Saved scaler for data preprocessing
- `robustscaler_enc1024_layers1.h5`: Saved autoencoder model
- `best_refined_model_RS.pkl`: Trained ML model for prediction
