Bootstrap: docker
From: ubuntu:20.04

%labels
    MAINTAINER Lorenzo
    DESCRIPTION Singularity container for GTDB-Tk with Python

%environment
    export LC_ALL=C
    export PATH="/usr/local/bin:/opt/conda/bin:$PATH"
    export PYTHONPATH="/opt/conda/lib/python3.8/site-packages:$PYTHONPATH"
    export GTDBTK_DATA_PATH="/clusterfs/jgi/scratch/science/mgs/nelli/databases/gtdbtk/release220"

%post
    # Set non-interactive mode for apt and debconf
    export DEBIAN_FRONTEND=noninteractive
    export DEBCONF_NONINTERACTIVE_SEEN=true

    # Set timezone information (pre-configure tzdata)
    echo "tzdata tzdata/Areas select Etc" | debconf-set-selections
    echo "tzdata tzdata/Zones/Etc select UTC" | debconf-set-selections

    # Install system dependencies
    apt-get update && apt-get install -y --no-install-recommends \
        build-essential \
        ca-certificates \
        git \
        wget \
        curl \
        unzip \
        file \
        cmake \
        hmmer \
        prodigal \
        ncbi-blast+ \
        tzdata \
        && rm -rf /var/lib/apt/lists/*

    # Install Miniconda
    wget https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh -O /miniconda.sh
    bash /miniconda.sh -b -p /opt/conda
    rm /miniconda.sh

    # Add conda to PATH
    export PATH="/opt/conda/bin:$PATH"

    # Install pplacer
    mkdir -p /usr/local/bin
    cd /tmp
    wget https://github.com/matsen/pplacer/releases/download/v1.1.alpha19/pplacer-Linux-v1.1.alpha19.zip
    unzip pplacer-Linux-v1.1.alpha19.zip
    cp pplacer-Linux-v1.1.alpha19/pplacer /usr/local/bin/
    cp pplacer-Linux-v1.1.alpha19/guppy /usr/local/bin/
    cp pplacer-Linux-v1.1.alpha19/rppr /usr/local/bin/
    chmod +x /usr/local/bin/pplacer /usr/local/bin/guppy /usr/local/bin/rppr
    rm -rf pplacer-Linux-v1.1.alpha19*

    # Install FastANI
    cd /tmp
    wget https://github.com/ParBLiSS/FastANI/releases/download/v1.33/fastANI-Linux64-v1.33.zip
    unzip fastANI-Linux64-v1.33.zip
    cp fastANI /usr/local/bin/
    chmod +x /usr/local/bin/fastANI
    rm -rf fastANI*

    # Install Mash
    cd /tmp
    wget https://github.com/marbl/Mash/releases/download/v2.3/mash-Linux64-v2.3.tar
    tar -xf mash-Linux64-v2.3.tar
    cp mash-Linux64-v2.3/mash /usr/local/bin/
    chmod +x /usr/local/bin/mash
    rm -rf mash-Linux64-v2.3*

    # Install skani
    cd /tmp
    wget https://github.com/bluenote-1577/skani/releases/download/latest/skani
    chmod +x skani
    cp skani /usr/local/bin/
    rm -rf skani

    # Install Python packages
    /opt/conda/bin/pip install numpy pandas biopython

    # Install GTDB-Tk
    /opt/conda/bin/pip install gtdbtk

    # Verify installations
    echo "Verifying installations..."
    which pplacer
    which fastANI
    which mash
    which skani
    which gtdbtk

%runscript
    exec "$@"
