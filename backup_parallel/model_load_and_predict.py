#!/usr/bin/env python3
"""
Combined script to:
1. Load and verify a trained model
2. Make predictions on sample data using the model

Usage:
    python model_load_and_predict.py --verify-only  # Only verify the model
    python model_load_and_predict.py --predict      # Make predictions on sample data
    python model_load_and_predict.py                # Do both verification and prediction
"""

import os
import sys
import joblib
import argparse
import pandas as pd
import numpy as np


def parse_arguments():
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description='Model verification and prediction tool')
    parser.add_argument('--verify-only', action='store_true', 
                        help='Only verify the model without making predictions')
    parser.add_argument('--predict-only', action='store_true',
                        help='Only make predictions without detailed model verification')
    parser.add_argument('--model-path', type=str, default='best_refined_model_RS.pkl',
                        help='Path to the model file')
    parser.add_argument('--sample-path', type=str, default='encoded_final_results.csv',
                        help='Path to the sample data file')
    parser.add_argument('--output-path', type=str, default='prediction_results.csv',
                        help='Path to save prediction results')
    return parser.parse_args()


def verify_model(model_path):
    """Load and verify the model."""
    print("\n=== MODEL VERIFICATION ===")
    
    # Check if the model file exists
    if not os.path.exists(model_path):
        print(f"Model file not found: {model_path}")
        return None
    
    print(f"Model file found: {model_path}")
    print(f"File size: {os.path.getsize(model_path) / (1024*1024):.2f} MB")
    
    # Try to load the model
    try:
        model = joblib.load(model_path)
        print("Model loaded successfully!")
        print(f"Model type: {type(model)}")
        
        # Print model attributes
        print("\nModel attributes:")
        print(f"n_estimators: {model.n_estimators}")
        print(f"max_depth: {model.max_depth}")
        print(f"learning_rate: {model.learning_rate}")
        print(f"subsample: {model.subsample}")
        print(f"colsample_bytree: {model.colsample_bytree}")
        print(f"min_child_weight: {model.min_child_weight}")
        print(f"gamma: {model.gamma}")
        print(f"reg_alpha: {model.reg_alpha}")
        print(f"reg_lambda: {model.reg_lambda}")
        
        print("\nModel successfully verified!")
        return model
    except Exception as e:
        print(f"Error loading model: {e}")
        return None


def load_model(model_path, detailed_verification=False):
    """Load the model with optional detailed verification."""
    print(f"Loading model from {model_path}...")
    
    try:
        model = joblib.load(model_path)
        print("Model loaded successfully!")
        print(f"Model type: {type(model)}")
        
        if detailed_verification:
            # Print model attributes
            print("\nModel attributes:")
            print(f"n_estimators: {model.n_estimators}")
            print(f"max_depth: {model.max_depth}")
            print(f"learning_rate: {model.learning_rate}")
            print(f"subsample: {model.subsample}")
            print(f"colsample_bytree: {model.colsample_bytree}")
            print(f"min_child_weight: {model.min_child_weight}")
            print(f"gamma: {model.gamma}")
            print(f"reg_alpha: {model.reg_alpha}")
            print(f"reg_lambda: {model.reg_lambda}")
        
        return model
    except Exception as e:
        print(f"Error loading model: {e}")
        sys.exit(1)


def load_sample_data(sample_path):
    """Load the sample data."""
    print(f"Loading sample data from {sample_path}...")
    
    try:
        sample_df = pd.read_csv(sample_path)
        print(f"Sample data loaded successfully! Shape: {sample_df.shape}")
        return sample_df
    except Exception as e:
        print(f"Error loading sample data: {e}")
        sys.exit(1)


def prepare_features(sample_df, model):
    """Prepare features for prediction."""
    print("\nChecking data compatibility...")
    
    # Columns to remove before prediction (metadata columns)
    columns_to_remove = ['Assembly', 'Domain', 'Phylum', 'Class', 'Order', 
                         'Family', 'Genus', 'Species', 'Genome accessions']
    present_columns_to_remove = [col for col in columns_to_remove if col in sample_df.columns]
    
    # Check if 'Label' column exists in the sample
    has_label = 'Label' in sample_df.columns
    if has_label:
        print("Note: 'Label' column found in sample data. This will be removed for prediction.")
        present_columns_to_remove.append('Label')
    
    # Remove metadata columns
    X_sample = sample_df.drop(columns=present_columns_to_remove, errors='ignore')
    print(f"Sample features shape after removing metadata: {X_sample.shape}")
    
    # Check if the number of features matches what the model expects
    n_features_expected = model.n_features_in_ if hasattr(model, 'n_features_in_') else None
    if n_features_expected is not None:
        print(f"Model expects {n_features_expected} features")
        if X_sample.shape[1] == n_features_expected:
            print("✅ Number of features matches model expectations!")
        else:
            print(f"❌ Feature count mismatch! Sample has {X_sample.shape[1]} features, but model expects {n_features_expected}.")
            
            # Try to fix by checking feature names
            if hasattr(model, 'feature_names_in_'):
                print("Attempting to fix feature mismatch using feature names...")
                model_features = model.feature_names_in_
                sample_features = X_sample.columns.tolist()
                
                missing_features = [f for f in model_features if f not in sample_features]
                extra_features = [f for f in sample_features if f not in model_features]
                
                print(f"Missing features: {len(missing_features)}")
                print(f"Extra features: {len(extra_features)}")
                
                if len(missing_features) > 0:
                    print("Adding missing features with zero values...")
                    for feature in missing_features:
                        X_sample[feature] = 0.0
                
                if len(extra_features) > 0:
                    print("Removing extra features...")
                    X_sample = X_sample.drop(columns=extra_features)
                
                # Reorder columns to match model's expected order
                X_sample = X_sample[model_features]
                print(f"Fixed sample shape: {X_sample.shape}")
            else:
                print("Cannot fix feature mismatch: model doesn't have feature_names_in_ attribute.")
                sys.exit(1)
    
    return X_sample, has_label


def make_predictions(model, X_sample, sample_df, has_label, output_path):
    """Make predictions using the model."""
    print("\nMaking predictions...")
    
    try:
        y_pred_prob = model.predict_proba(X_sample)[:, 1]
        y_pred = (y_pred_prob >= 0.5).astype(int)
        
        # Create results DataFrame
        results = pd.DataFrame({
            'Sample': sample_df['Assembly'] if 'Assembly' in sample_df.columns else range(len(X_sample)),
            'Species': sample_df['Species'] if 'Species' in sample_df.columns else ['Unknown'] * len(X_sample),
            'Probability': y_pred_prob,
            'Prediction': y_pred
        })
        
        # Map prediction to class label
        results['Class'] = results['Prediction'].map({0: 'Non-pathogenic', 1: 'Pathogenic'})
        
        # Save results
        results.to_csv(output_path, index=False)
        print(f"✅ Predictions saved to {output_path}")
        
        # Display results
        print("\nPrediction Results:")
        print(results)
        
        # If the sample had a true label, calculate accuracy
        if has_label:
            y_true = sample_df['Label'].values
            accuracy = (y_pred == y_true).mean()
            print(f"\nAccuracy on this sample: {accuracy:.4f}")
        
        return results
    except Exception as e:
        print(f"Error making predictions: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


def main():
    """Main function to run the script."""
    args = parse_arguments()
    
    # Determine what operations to perform
    do_verification = not args.predict_only
    do_prediction = not args.verify_only
    
    model = None
    
    # Verify model if requested
    if do_verification:
        model = verify_model(args.model_path)
        if model is None:
            print("Model verification failed. Exiting.")
            sys.exit(1)
    
    # Make predictions if requested
    if do_prediction:
        # Load model if not already loaded
        if model is None:
            model = load_model(args.model_path)
        
        # Load and prepare sample data
        sample_df = load_sample_data(args.sample_path)
        X_sample, has_label = prepare_features(sample_df, model)
        
        # Make predictions
        results = make_predictions(model, X_sample, sample_df, has_label, args.output_path)
        
        print("\nPrediction process completed successfully!")


if __name__ == "__main__":
    main()
