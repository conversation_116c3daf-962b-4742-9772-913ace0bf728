// Nextflow configuration file for the genomic data processing workflow

// Default parameters
params {
    // Input/output parameters
    genome = "$baseDir/GCF_008369605.1.fna"
    outdir = "$baseDir/results"

    // Database paths
    gtdbtk_db = "/clusterfs/jgi/scratch/science/mgs/nelli/databases/gtdbtk/release220"
    hmm_base_dir = "/clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/1_pangenomes_features/genera_pangenome_est_30_Sept/hmm_est_5_30_Sept"

    // Batch processing parameters
    batch_count = 4  // Number of batches to process

    // Resource parameters
    max_cpus = 16
    max_memory = 32.GB
}

// Process-specific configurations
process {
    // Default container settings
    containerOptions = '--bind /clusterfs'

    withName: PRODIGAL {
        container = "$baseDir/containers/prodigal.sif"
        cpus = 1
        memory = 4.GB
    }

    withName: GTDBTK {
        container = "$baseDir/containers/gtdbtk.sif"
        cpus = params.max_cpus
        memory = params.max_memory
    }

    withName: 'HMMSEARCH' {
        container = "$baseDir/containers/hmmer.sif"
        cpus = params.max_cpus
        memory = 8.GB
    }

    withName: 'PROCESS_HITS' {
        container = "$baseDir/containers/hmmer.sif"
        cpus = 2
        memory = 4.GB
    }

    withName: CREATE_DATAFRAME {
        container = "$baseDir/containers/hmmer.sif"
        cpus = 4
        memory = 8.GB
    }
}

// Singularity configuration
singularity {
    enabled = true
    autoMounts = true
    cacheDir = "$baseDir/singularity_cache"
}

// Execution profiles
profiles {
    standard {
        process.executor = 'local'
    }
}

// Manifest
manifest {
    name = 'Genomic Data Processing Workflow'
    author = 'Lorenzo'
    description = 'Nextflow pipeline for processing genomic data with Prodigal, GTDB-Tk, and HMMER'
    version = '1.0.0'
    mainScript = 'main.nf'
    nextflowVersion = '>=20.10.0'
}
