# Genomic Data Processing Workflow

This workflow processes a genomic file (GCF_008369605.1.fna) through a series of steps:

1. First, run Prodigal to annotate proteins and rename headers:
   python run_prodigal_and_rename.py GCF_008369605.1.fna
   
   This produces GCF_008369605.1.faa with renamed protein headers.

2. Next, run GTDB-Tk to classify the genome taxonomically:
   python run_gtdbtk.py --genome_dir . --clean_outputs
   
   This generates gtdb_classification.csv with taxonomic information.

3. Then, search the protein sequences against HMM profiles:
   python hmmsearch_python.py /clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/1_pangenomes_features/genera_pangenome_est_30_Sept/hmm_est_5_30_Sept/batch_1 GCF_008369605.1.faa hits/batch_1
   
   This creates .tbl files in the hits/batch_1 directory.

4. Convert the .tbl files to .csv format:
   python process_single_batch.py hits/batch_1 csv_hits/batch_1
   
   This generates .csv files in the csv_hits/batch_1 directory.

5. Finally, create a unified dataframe with hit scores and taxonomic information:
   python create_unique_df_hits_optimized.py csv_hits gtdb_classification.csv output.csv
   
   This produces output.csv containing the final results.
```
