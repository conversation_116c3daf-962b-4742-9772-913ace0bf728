#!/bin/bash
set -e

# Default values
THREADS=1
INPUT_DIR="/data/input"
OUTPUT_DIR="/data/output"
MIN_CONTIG_LENGTH=500
MEMORY=16
MODE="meta"

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case "$1" in
        --threads)
            THREADS="$2"
            shift 2
            ;;
        --input)
            INPUT_DIR="$2"
            shift 2
            ;;
        --output)
            OUTPUT_DIR="$2"
            shift 2
            ;;
        --min-length)
            MIN_CONTIG_LENGTH="$2"
            shift 2
            ;;
        --memory)
            MEMORY="$2"
            shift 2
            ;;
        --mode)
            MODE="$2"
            shift 2
            ;;
        *)
            echo "Unknown option: $1"
            echo "Usage: $0 --threads <num_threads> --input <input_dir> --output <output_dir> [--min-length <min_contig_length>] [--memory <memory_in_GB>] [--mode <meta|isolate|rna|plasmid|metaviral|metaplasmid>]"
            exit 1
            ;;
    esac
done

# Create output directory if it doesn't exist
mkdir -p "$OUTPUT_DIR"
mkdir -p "$OUTPUT_DIR/filtered"

echo "Starting assembly with the following parameters:"
echo "Threads: $THREADS"
echo "Input directory: $INPUT_DIR"
echo "Output directory: $OUTPUT_DIR"
echo "Minimum contig length: $MIN_CONTIG_LENGTH"
echo "Memory: $MEMORY GB"
echo "Assembly mode: $MODE"

# Find all FASTQ files in the input directory
FASTQ_FILES=()
for ext in fastq fastq.gz fq fq.gz; do
    for file in "$INPUT_DIR"/*.$ext; do
        if [ -e "$file" ]; then
            FASTQ_FILES+=("$file")
        fi
    done
done

if [ ${#FASTQ_FILES[@]} -eq 0 ]; then
    echo "Error: No FASTQ files found in $INPUT_DIR"
    exit 1
fi

# Determine if we have paired-end or single-end reads
PAIRED=false
if [ ${#FASTQ_FILES[@]} -ge 2 ]; then
    # Check if files follow naming convention for paired-end reads
    for file in "${FASTQ_FILES[@]}"; do
        if [[ $file =~ _R1_ || $file =~ _1\. ]]; then
            PAIRED=true
            break
        fi
    done
fi

# Prepare SPAdes command based on mode
SPADES_CMD="spades.py"
case "$MODE" in
    meta)
        SPADES_CMD="metaspades.py"
        ;;
    rna)
        SPADES_CMD="rnaspades.py"
        ;;
    plasmid)
        SPADES_CMD="plasmidspades.py"
        ;;
    metaviral)
        SPADES_CMD="metaviralspades.py"
        ;;
    metaplasmid)
        SPADES_CMD="metaplasmidspades.py"
        ;;
    *)
        # Default is isolate mode (spades.py)
        ;;
esac

# Prepare read arguments for SPAdes
if [ "$PAIRED" = true ]; then
    # Find and sort paired-end reads
    R1_FILES=()
    R2_FILES=()
    
    for file in "${FASTQ_FILES[@]}"; do
        if [[ $file =~ _R1_ || $file =~ _1\. ]]; then
            R1_FILES+=("$file")
        elif [[ $file =~ _R2_ || $file =~ _2\. ]]; then
            R2_FILES+=("$file")
        fi
    done
    
    # Sort the arrays to ensure matching pairs
    R1_FILES=($(printf '%s\n' "${R1_FILES[@]}" | sort))
    R2_FILES=($(printf '%s\n' "${R2_FILES[@]}" | sort))
    
    if [ ${#R1_FILES[@]} -ne ${#R2_FILES[@]} ]; then
        echo "Warning: Unequal number of forward and reverse read files. Using single-end mode."
        READ_ARGS=""
        for file in "${FASTQ_FILES[@]}"; do
            READ_ARGS="$READ_ARGS --s1 $file"
        done
    else
        READ_ARGS=""
        for ((i=0; i<${#R1_FILES[@]}; i++)); do
            READ_ARGS="$READ_ARGS --pe1-1 ${R1_FILES[$i]} --pe1-2 ${R2_FILES[$i]}"
        done
    fi
else
    # Single-end mode
    READ_ARGS=""
    for file in "${FASTQ_FILES[@]}"; do
        READ_ARGS="$READ_ARGS --s1 $file"
    done
fi

# Run SPAdes
echo "Running SPAdes assembly..."
$SPADES_CMD $READ_ARGS -o "$OUTPUT_DIR/spades_output" -t "$THREADS" -m "$MEMORY"

# Check if assembly was successful
if [ ! -f "$OUTPUT_DIR/spades_output/contigs.fasta" ]; then
    echo "Error: SPAdes assembly failed. Check the log file for details."
    exit 1
fi

# Filter contigs by length
echo "Filtering contigs by minimum length of $MIN_CONTIG_LENGTH bp..."
python3 /usr/local/bin/filtermultifa_length.py "$OUTPUT_DIR/spades_output/contigs.fasta" $MIN_CONTIG_LENGTH 10000000 "$OUTPUT_DIR/filtered/contigs_filtered.fasta"

# Copy the SPAdes log file to the output directory
cp "$OUTPUT_DIR/spades_output/spades.log" "$OUTPUT_DIR/spades.log"

echo "Assembly completed successfully!"
echo "Filtered contigs are available at: $OUTPUT_DIR/filtered/contigs_filtered.fasta"
