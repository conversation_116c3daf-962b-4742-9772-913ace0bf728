#!/usr/bin/env python3
import argparse
import pandas as pd
import sys

def main():
    parser = argparse.ArgumentParser(
        description="Combine per-sample depth files into a single contig_stats table."
    )
    parser.add_argument(
        "--depth-files",
        nargs="+",
        required=True,
        help="List of per-sample depth files (TSV format)."
    )
    parser.add_argument(
        "--out",
        required=True,
        help="Output combined contig stats file (TSV)."
    )
    args = parser.parse_args()

    dfs = []
    for file in args.depth_files:
        try:
            df = pd.read_csv(file, sep="\t")
            dfs.append(df)
        except Exception as e:
            sys.stderr.write(f"Error reading {file}: {e}\n")
            sys.exit(1)

    if not dfs:
        sys.stderr.write("No input files provided.\n")
        sys.exit(1)

    combined = pd.concat(dfs, ignore_index=True)
    combined.to_csv(args.out, sep="\t", index=False)
    print(f"Combined results saved to {args.out}")

if __name__ == "__main__":
    main()
