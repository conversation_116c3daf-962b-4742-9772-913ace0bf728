# workflow/scripts/filtermultifa_length.py

from Bio import SeqIO
import sys
import os

def filter_by_length(input_file, min_length, max_length, output_file):
  """
  Filters sequences from a multi-FASTA file based on length and writes them to an output file.

  Parameters:
  - input_file: Path to the input multi-FASTA file.
  - min_length: Minimum length of sequences to include.
  - max_length: Maximum length of sequences to include.
  - output_file: Path to the output file where filtered sequences will be written.
  """
  min_length = int(min_length)
  max_length = int(max_length)
  assembly_base_name = os.path.basename(os.path.dirname(input_file))

  with open(output_file, 'w') as out_f:
      for seq_record in SeqIO.parse(input_file, "fasta"):
          seq_len = len(seq_record.seq)
          if min_length <= seq_len <= max_length:
              header = f">{assembly_base_name}|{seq_record.description}\n"
              sequence = f"{str(seq_record.seq)}\n"
              out_f.write(header + sequence)

if __name__ == "__main__":
  if len(sys.argv) != 5:
      print("Usage: filtermultifa_length.py <input_file> <min_length> <max_length> <output_file>")
      sys.exit(1)
  input_file = sys.argv[1]
  min_length = sys.argv[2]
  max_length = sys.argv[3]
  output_file = sys.argv[4]

  filter_by_length(input_file, min_length, max_length, output_file)
