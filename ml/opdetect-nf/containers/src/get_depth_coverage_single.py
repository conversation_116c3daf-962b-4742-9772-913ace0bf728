#!/usr/bin/env python3

import pysam
import numpy as np
import pandas as pd
import argparse
import os
from Bio import SeqIO

def parse_fasta_lengths_and_gc(fasta_path):
    """
    Parse a FASTA to get per-contig length and GC%.
    Returns dict: contig_name -> (length, GC_fraction).
    """
    contig_stats = {}
    if not os.path.isfile(fasta_path):
        return contig_stats  # return empty if not found

    for record in SeqIO.parse(fasta_path, "fasta"):
        seq = record.seq.upper()
        length = len(seq)
        gc_count = seq.count('G') + seq.count('C')
        gc_percent = (100.0 * gc_count / length) if length > 0 else 0.0
        contig_stats[record.id] = (length, gc_percent)
    return contig_stats


def analyze_bam(bam_file, fasta_path, min_mapq, paired, sample_name, out_file):
    """
    Analyze a single BAM for coverage. Writes out a TSV with:
      Sample, Reference, Length_bp, GC_percent, Avg_depth, Coverage_percent, variance
    """
    # Parse assembly stats
    contig2stats = parse_fasta_lengths_and_gc(fasta_path)

    bam = pysam.AlignmentFile(bam_file, "rb")
    ref_lengths = dict(zip(bam.references, bam.lengths))

    # Initialize depth arrays
    depths = {}
    for ref_name, length in ref_lengths.items():
        depths[ref_name] = np.zeros(length, dtype=np.int32)

    # Traverse reads
    for read in bam.fetch():
        if read.is_unmapped:
            continue
        if read.mapping_quality < min_mapq:
            continue
        if paired == 'y' and not read.is_proper_pair:
            continue

        ref_name = bam.get_reference_name(read.reference_id)
        for pos in read.get_reference_positions():
            if 0 <= pos < len(depths[ref_name]):
                depths[ref_name][pos] += 1

    # Summarize coverage
    rows = []
    for ref_name, length in ref_lengths.items():
        depth_array = depths[ref_name]

        avg_depth = float(np.mean(depth_array))
        var_depth = float(np.var(depth_array))
        covered_bases = np.sum(depth_array > 0)
        coverage_pct = (100.0 * covered_bases / length) if length > 0 else 0.0

        if ref_name in contig2stats:
            contig_len, gc_val = contig2stats[ref_name]
        else:
            # Fallback if there's no contig match
            contig_len, gc_val = length, 0.0

        rows.append({
            'Sample': sample_name,
            'Reference': ref_name,
            'Length_bp': contig_len,
            'GC_percent': gc_val,
            'Avg_depth': avg_depth,
            'Coverage_percent': coverage_pct,
            'variance': var_depth
        })

    bam.close()
    df = pd.DataFrame(rows)
    # Write out
    df.to_csv(out_file, index=False, sep="\t")


def main():
    parser = argparse.ArgumentParser(
        description="Analyze per-contig coverage & GC% for a single BAM + assembly."
    )
    parser.add_argument("--bam", required=True,
                        help="Path to the .bam file")
    parser.add_argument("--assembly", required=True,
                        help="Path to the corresponding .fna assembly for the sample")
    parser.add_argument("--sample-name", required=True,
                        help="Sample name")
    parser.add_argument("--mapq", type=int, default=10,
                        help="Minimum MAPQ value for filtering (default=10)")
    parser.add_argument("--paired", choices=['y', 'n'], default='n',
                        help="Filter for properly paired reads (y) or not (n) (default=n)")
    parser.add_argument("--out", required=True,
                        help="Output file name (e.g., {sample}_depth.tab)")

    args = parser.parse_args()

    analyze_bam(
        bam_file=args.bam,
        fasta_path=args.assembly,
        min_mapq=args.mapq,
        paired=args.paired,
        sample_name=args.sample_name,
        out_file=args.out
    )

if __name__ == "__main__":
    main()
