#!/bin/bash
# Script to generate mapping statistics from BAM files

# Parse arguments
SELF_BAM=$1
PATHOGEN_BAM=$2
OUTPUT_DIR=$3
SAMPLE_ID=$4

# Create output directory if it doesn't exist
mkdir -p $OUTPUT_DIR

# Generate self-mapping statistics
echo -e "sample\tcontig\tlength\tmapped_reads\tcoverage\tmean_depth\tmedian_depth\tmin_depth\tmax_depth\tstd_depth" > $OUTPUT_DIR/mapping_stats_self.tsv

# Extract contig names and lengths from the BAM file
samtools view -H $SELF_BAM | grep "@SQ" | while read line; do
    contig=$(echo $line | awk '{print $2}' | sed 's/SN://')
    length=$(echo $line | awk '{print $3}' | sed 's/LN://' | tr -d '\r')

    # Count mapped reads for this contig
    mapped_reads=$(samtools view -c -F 0x904 $SELF_BAM $contig)

    # Calculate coverage and depth statistics
    if [ $mapped_reads -gt 0 ]; then
        # Calculate coverage (mapped_reads * read_length / contig_length)
        # Assuming read length of 150 for simplicity
        coverage=$(echo "scale=4; $mapped_reads * 150 / $length" | bc)

        # Get depth statistics using a temporary file
        depth_file=$(mktemp)
        samtools depth -r $contig $SELF_BAM > $depth_file

        if [ -s $depth_file ]; then
            # Calculate depth statistics
            mean_depth=$(awk '{sum+=$3} END {print sum/NR}' $depth_file)
            median_depth=$(sort -n -k3,3 $depth_file | awk '{a[NR]=$3} END {if (NR%2==1) print a[int(NR/2)+1]; else print (a[NR/2]+a[NR/2+1])/2}')
            min_depth=$(sort -n -k3,3 $depth_file | head -n1 | awk '{print $3}')
            max_depth=$(sort -n -k3,3 $depth_file | tail -n1 | awk '{print $3}')
            std_depth=$(awk '{sum+=$3; sumsq+=$3*$3} END {print sqrt(sumsq/NR - (sum/NR)^2)}' $depth_file)

            # Clean up
            rm $depth_file
        else
            mean_depth=0
            median_depth=0
            min_depth=0
            max_depth=0
            std_depth=0
        fi

        # Write to output file
        echo -e "$SAMPLE_ID\t$contig\t$length\t$mapped_reads\t$coverage\t$mean_depth\t$median_depth\t$min_depth\t$max_depth\t$std_depth" >> $OUTPUT_DIR/mapping_stats_self.tsv
    fi
done

# Generate pathogen mapping statistics
echo -e "sample\tpathogen\tlength\tmapped_reads\tcoverage\tmean_depth\tmedian_depth\tmin_depth\tmax_depth\tstd_depth" > $OUTPUT_DIR/mapping_stats_pathdb.tsv

# Extract pathogen names and lengths from the BAM file
samtools view -H $PATHOGEN_BAM | grep "@SQ" | while read line; do
    pathogen=$(echo $line | awk '{print $2}' | sed 's/SN://')
    length=$(echo $line | awk '{print $3}' | sed 's/LN://' | tr -d '\r')

    # Count mapped reads for this pathogen
    mapped_reads=$(samtools view -c -F 0x904 $PATHOGEN_BAM $pathogen)

    # Calculate coverage and depth statistics
    if [ $mapped_reads -gt 0 ]; then
        # Calculate coverage (mapped_reads * read_length / pathogen_length)
        # Assuming read length of 150 for simplicity
        coverage=$(echo "scale=4; $mapped_reads * 150 / $length" | bc)

        # Get depth statistics using a temporary file
        depth_file=$(mktemp)
        samtools depth -r $pathogen $PATHOGEN_BAM > $depth_file

        if [ -s $depth_file ]; then
            # Calculate depth statistics
            mean_depth=$(awk '{sum+=$3} END {print sum/NR}' $depth_file)
            median_depth=$(sort -n -k3,3 $depth_file | awk '{a[NR]=$3} END {if (NR%2==1) print a[int(NR/2)+1]; else print (a[NR/2]+a[NR/2+1])/2}')
            min_depth=$(sort -n -k3,3 $depth_file | head -n1 | awk '{print $3}')
            max_depth=$(sort -n -k3,3 $depth_file | tail -n1 | awk '{print $3}')
            std_depth=$(awk '{sum+=$3; sumsq+=$3*$3} END {print sqrt(sumsq/NR - (sum/NR)^2)}' $depth_file)

            # Clean up
            rm $depth_file
        else
            mean_depth=0
            median_depth=0
            min_depth=0
            max_depth=0
            std_depth=0
        fi

        # Write to output file
        echo -e "$SAMPLE_ID\t$pathogen\t$length\t$mapped_reads\t$coverage\t$mean_depth\t$median_depth\t$min_depth\t$max_depth\t$std_depth" >> $OUTPUT_DIR/mapping_stats_pathdb.tsv
    fi
done

echo "Mapping statistics written to $OUTPUT_DIR"
