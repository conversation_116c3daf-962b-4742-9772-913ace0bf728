Bootstrap: docker
From: debian:bullseye-slim

%setup
    # Directories for mounting data and results from the host
    mkdir -p ${SINGULARITY_ROOTFS}/data
    mkdir -p ${SINGULARITY_ROOTFS}/results/checkv
    mkdir -p ${SINGULARITY_ROOTFS}/checkv_db

%post
    # Install basic dependencies
    apt-get update
    apt-get install -y --no-install-recommends \
        wget \
        ca-certificates \
        curl \
        procps \
        python3 \
        python3-pip \
        git

    # Install Pixi
    cd /opt
    wget https://github.com/prefix-dev/pixi/releases/latest/download/pixi-x86_64-unknown-linux-musl.tar.gz
    mkdir -p pixi_tmp
    tar -xzf pixi-x86_64-unknown-linux-musl.tar.gz -C pixi_tmp
    cp pixi_tmp/pixi /usr/local/bin/
    chmod +x /usr/local/bin/pixi
    rm -rf pixi_tmp pixi-x86_64-unknown-linux-musl.tar.gz

    # Verify pixi installation
    pixi --version

    # Install CheckV globally using pixi
    echo "Installing CheckV globally with Pixi..."
    pixi global install -c conda-forge -c bioconda checkv

    # Verify CheckV installation
    export PATH="/root/.pixi/bin:$PATH"
    checkv --help || echo "Warning: CheckV installation may have issues"

    # Ensure the results directory exists
    mkdir -p /results/checkv

    # Clean up
    apt-get clean
    rm -rf /var/lib/apt/lists/*

%environment
    export LC_ALL=C
    export PATH="/root/.pixi/bin:$PATH"
    export SHELL=/bin/bash

%runscript
    #!/bin/bash
    # Using bash explicitly in shebang just in case

    # Default arguments
    THREADS=1
    DB_PATH=""
    INPUT_FILE=""
    OUTPUT_DIR="/results/checkv"

    # Simple argument parsing
    while [[ $# -gt 0 ]]; do
        case "$1" in
            --threads)
                THREADS="$2"
                shift 2
                ;;
            --db)
                DB_PATH="$2"
                shift 2
                ;;
            --input)
                INPUT_FILE="$2"
                shift 2
                ;;
            --output)
                OUTPUT_DIR="$2"
                shift 2
                ;;
            *)
                echo "Unknown option: $1" >&2
                echo "Usage: apptainer run checkv-debian.sif --threads <num_threads> --db <path_to_database> --input <input_file> [--output <output_dir>]" >&2
                exit 1
                ;;
        esac
    done

    # Validate required arguments
    if [ -z "$DB_PATH" ] || [ -z "$INPUT_FILE" ]; then
        echo "Error: Missing required arguments --db and/or --input" >&2
        echo "Usage: apptainer run checkv-debian.sif --threads <num_threads> --db <path_to_database> --input <input_file> [--output <output_dir>]" >&2
        exit 1
    fi

    # Ensure output directory exists
    mkdir -p "$OUTPUT_DIR"

    echo "Running CheckV with $THREADS threads"
    echo "Database path: $DB_PATH"
    echo "Input file: $INPUT_FILE"
    echo "Results will be in $OUTPUT_DIR directory"

    # Execute CheckV end-to-end workflow
    checkv end_to_end "$INPUT_FILE" "$OUTPUT_DIR" -t "$THREADS" -d "$DB_PATH"

    echo "CheckV run finished."

%labels
    Author Your Name <<EMAIL>>
    Version 1.0-pixi-debian
    Description Apptainer container for CheckV viral sequence analysis using Pixi on Debian Linux

%help
    This container runs CheckV (installed via Pixi) for viral sequence quality assessment.

    The CheckV environment is managed by Pixi.

    Usage:
        apptainer run --bind /path/on/host/to/data:/data \
                      --bind /path/on/host/to/results:/results \
                      --bind /path/on/host/to/checkv_db:/checkv_db \
                      checkv-debian.sif \
                      --threads <num_threads> \
                      --db /checkv_db \
                      --input /data/your_input_file.fasta \
                      [--output /results/checkv]

    Arguments inside the container:
        --threads    Number of threads to use (default: 1)
        --db         Path *inside the container* to the CheckV database (e.g., /checkv_db if bound)
        --input      Path *inside the container* to the input file (e.g., /data/your_input_file.fasta if bound)
        --output     Path *inside the container* to the output directory (default: /results/checkv)

    Example Host Command assuming:
      - Input fasta is at: /home/<USER>/my_data/viruses.fna
      - Desired output dir: /home/<USER>/my_results
      - CheckV DB is at: /share/databases/checkv_db

    apptainer run --bind /home/<USER>/my_data:/data \
                  --bind /home/<USER>/my_results:/results \
                  --bind /share/databases/checkv_db:/checkv_db \
                  checkv-debian.sif \
                  --threads 8 \
                  --db /checkv_db \
                  --input /data/viruses.fna

    The results will be stored in the host directory bound to /results/checkv inside the container
    (e.g., /home/<USER>/my_results/checkv).
