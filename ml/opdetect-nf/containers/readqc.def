Bootstrap: docker
From: quay.io/biocontainers/bbmap:38.97--h5c4e2a8_1

%post
    # Install additional tools for read processing
    apt-get update && apt-get install -y --no-install-recommends \
        procps \
        python3-pip \
        && apt-get clean \
        && rm -rf /var/lib/apt/lists/*
    
    # Install seqkit for read statistics
    pip3 install seqkit

%environment
    export LC_ALL=C

%runscript
    exec "$@"

%labels
    Author fschulz
    Version v0.1.0
    Description Container for read processing in microbiome analysis pipeline
