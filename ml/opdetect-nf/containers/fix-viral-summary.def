Bootstrap: docker
From: debian:bullseye-slim

%help
    Container with tools to fix the viral screening summary file.

%labels
    Author Augment
    Version 1.0.0

%post
    # Install dependencies
    apt-get update && apt-get install -y --no-install-recommends \
        bash \
        grep \
        sed \
        awk \
        coreutils \
        && apt-get clean \
        && rm -rf /var/lib/apt/lists/*

    # Create script directory
    mkdir -p /usr/local/bin

    # Add the fix script
    cat > /usr/local/bin/fix_viral_summary.sh << 'EOF'
#!/bin/bash

# Script to fix the viral screening summary file

# Check if arguments are provided
if [ $# -lt 3 ]; then
    echo "Usage: $0 <viral_summary_file> <genomad_summary_file> <mapping_stats_file>"
    echo "Example: $0 results/04viralscreening/sample/sample_viral_screening_summary.tsv results/04viralscreening/sample/genomad/sample_summary/sample_virus_summary.tsv results/02mapping/mapping_stats_self.tsv"
    exit 1
fi

VIRAL_SUMMARY=$1
GENOMAD_SUMMARY=$2
MAPPING_STATS=$3
SAMPLE_ID=$(basename $VIRAL_SUMMARY _viral_screening_summary.tsv)

# Check if the file exists
if [ ! -f "$VIRAL_SUMMARY" ]; then
    echo "Viral screening summary file not found: $VIRAL_SUMMARY"
    exit 1
fi

# Create a temporary file
cp $VIRAL_SUMMARY ${VIRAL_SUMMARY}.bak

# Get the genomad taxonomy information
if [ -f "$GENOMAD_SUMMARY" ]; then
    echo "Getting taxonomy information from geNomad..."
    # Use cut to extract the taxonomy field (column 11)
    tail -n +2 $GENOMAD_SUMMARY | cut -f1,11 > taxonomy.tmp
else
    echo "geNomad summary file not found: $GENOMAD_SUMMARY"
    touch taxonomy.tmp
fi

# Get the contig depth information
if [ -f "$MAPPING_STATS" ]; then
    echo "Getting contig depth information from mapping results..."
    grep "$SAMPLE_ID" $MAPPING_STATS | cut -f2,6 > depth.tmp
else
    echo "Mapping stats file not found: $MAPPING_STATS"
    touch depth.tmp
fi

# Create a new file with the correct header
echo -e "sample_id\tcontig_id\tlength\tgenomic_type\tviral_score\ttaxonomy\tcheckv_quality\tcompleteness\trdrp_hit\tcontig_depth" > ${VIRAL_SUMMARY}.fixed

# Process each line
tail -n +2 ${VIRAL_SUMMARY}.bak | while read line; do
    # Extract fields
    sample=$(echo "$line" | cut -f1)
    contig=$(echo "$line" | cut -f2)
    length_type=$(echo "$line" | cut -f3)
    viral_score=$(echo "$line" | cut -f5)
    taxonomy=$(echo "$line" | cut -f6)
    checkv_quality=$(echo "$line" | cut -f7)
    completeness=$(echo "$line" | cut -f8)
    rdrp_hit=$(echo "$line" | cut -f9)
    contig_depth=$(echo "$line" | cut -f10)
    
    # Extract length and genomic_type
    if [[ "$length_type" == *"No terminal repeats"* ]]; then
        genomic_type="No terminal repeats"
        length=$(echo "$length_type" | sed 's/No terminal repeats//')
    elif [[ "$length_type" == *"Direct terminal repeats"* ]]; then
        genomic_type="Direct terminal repeats"
        length=$(echo "$length_type" | sed 's/Direct terminal repeats//')
    elif [[ "$length_type" == *"Inverted terminal repeats"* ]]; then
        genomic_type="Inverted terminal repeats"
        length=$(echo "$length_type" | sed 's/Inverted terminal repeats//')
    else
        genomic_type="Unknown"
        length=$length_type
    fi
    
    # Get taxonomy from geNomad
    if [ -f "taxonomy.tmp" ]; then
        taxonomy_info=$(grep -w "$contig" taxonomy.tmp | cut -f2)
        if [ ! -z "$taxonomy_info" ]; then
            taxonomy=$taxonomy_info
        fi
    fi
    
    # Get contig depth from mapping results
    if [ -f "depth.tmp" ]; then
        depth_info=$(grep -w "$contig" depth.tmp | cut -f2)
        if [ ! -z "$depth_info" ]; then
            contig_depth=$depth_info
        fi
    fi
    
    # Write to fixed file
    echo -e "$sample\t$contig\t$length\t$genomic_type\t$viral_score\t$taxonomy\t$checkv_quality\t$completeness\t$rdrp_hit\t$contig_depth" >> ${VIRAL_SUMMARY}.fixed
done

# Replace the original file with the fixed file
mv ${VIRAL_SUMMARY}.fixed $VIRAL_SUMMARY

# Clean up temporary files
rm -f taxonomy.tmp depth.tmp ${VIRAL_SUMMARY}.bak

echo "Viral screening summary file fixed: $VIRAL_SUMMARY"
EOF

    # Make the script executable
    chmod +x /usr/local/bin/fix_viral_summary.sh

%runscript
    exec /usr/local/bin/fix_viral_summary.sh "$@"
