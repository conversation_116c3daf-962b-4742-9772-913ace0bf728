Bootstrap: docker
From: quay.io/biocontainers/spades:3.15.5--h95f258a_1

%post
    # Install additional tools for assembly processing
    apt-get update && apt-get install -y --no-install-recommends \
        procps \
        python3-pip \
        && apt-get clean \
        && rm -rf /var/lib/apt/lists/*
    
    # Install biopython for sequence processing
    pip3 install biopython

%environment
    export LC_ALL=C

%runscript
    exec "$@"

%labels
    Author fschulz
    Version v0.1.0
    Description Container for assembly in microbiome analysis pipeline
