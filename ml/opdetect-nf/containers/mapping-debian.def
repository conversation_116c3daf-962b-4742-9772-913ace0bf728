Bootstrap: docker
From: debian:bullseye-slim

# Mapping Container
# Date: 2025-04-28
# Author: <PERSON><PERSON><PERSON>
# Description: Container for read mapping using bwa-mem2 and samtools

%setup
    # Directories for mounting data and results from the host
    mkdir -p ${SINGULARITY_ROOTFS}/data/input
    mkdir -p ${SINGULARITY_ROOTFS}/data/output
    mkdir -p ${SINGULARITY_ROOTFS}/reference

%files
    containers/src/get_depth_coverage_single.py /usr/local/bin/get_depth_coverage_single.py
    containers/src/combine_contig_stats.py /usr/local/bin/combine_contig_stats.py
    containers/src/generate_mapping_stats.sh /usr/local/bin/generate_mapping_stats.sh

%post
    # Install basic dependencies
    apt-get update
    apt-get install -y --no-install-recommends \
        wget \
        ca-certificates \
        curl \
        procps \
        python3 \
        python3-pip \
        git

    # Install Pixi
    cd /opt
    wget https://github.com/prefix-dev/pixi/releases/latest/download/pixi-x86_64-unknown-linux-musl.tar.gz
    mkdir -p pixi_tmp
    tar -xzf pixi-x86_64-unknown-linux-musl.tar.gz -C pixi_tmp
    cp pixi_tmp/pixi /usr/local/bin/
    chmod +x /usr/local/bin/pixi
    rm -rf pixi_tmp pixi-x86_64-unknown-linux-musl.tar.gz

    # Verify pixi installation
    pixi --version

    # Install bwa-mem2 and samtools globally using pixi
    echo "Installing bwa-mem2 and samtools globally with Pixi..."
    pixi global install -c conda-forge -c bioconda bwa-mem2 samtools pysam pandas numpy

    # Verify installations
    export PATH="/root/.pixi/bin:$PATH"
    bwa-mem2 --version || echo "Warning: bwa-mem2 installation may have issues"
    samtools --version || echo "Warning: samtools installation may have issues"

    # Install Python dependencies
    pip3 install pysam pandas numpy biopython

    # Make scripts executable
    chmod +x /usr/local/bin/get_depth_coverage_single.py
    chmod +x /usr/local/bin/combine_contig_stats.py
    chmod +x /usr/local/bin/generate_mapping_stats.sh

    # Clean up
    apt-get clean
    rm -rf /var/lib/apt/lists/*

%environment
    export LC_ALL=C
    export PATH="/root/.pixi/bin:$PATH"
    export SHELL=/bin/bash

%runscript
    #!/bin/bash
    # Using bash explicitly in shebang just in case

    # Default arguments
    THREADS=1
    READS=""
    REFERENCE=""
    OUTPUT_DIR=""
    SAMPLE=""

    # Simple argument parsing
    while [[ $# -gt 0 ]]; do
        case "$1" in
            --threads)
                THREADS="$2"
                shift 2
                ;;
            --reads)
                READS="$2"
                shift 2
                ;;
            --reference)
                REFERENCE="$2"
                shift 2
                ;;
            --output-dir)
                OUTPUT_DIR="$2"
                shift 2
                ;;
            --sample)
                SAMPLE="$2"
                shift 2
                ;;
            *)
                echo "Unknown option: $1" >&2
                echo "Usage: apptainer run mapping-debian.sif --threads <num_threads> --reads <reads_file> --reference <reference_file> --output-dir <output_dir> --sample <sample_name>" >&2
                exit 1
                ;;
        esac
    done

    # Validate required arguments
    if [ -z "$READS" ] || [ -z "$REFERENCE" ] || [ -z "$OUTPUT_DIR" ]; then
        echo "Error: Missing required arguments --reads, --reference, and/or --output-dir" >&2
        echo "Usage: apptainer run mapping-debian.sif --threads <num_threads> --reads <reads_file> --reference <reference_file> --output-dir <output_dir> --sample <sample_name>" >&2
        exit 1
    fi

    # Get sample name from reads file if not provided
    if [ -z "$SAMPLE" ]; then
        SAMPLE=$(basename "$READS" .fastq.gz)
    fi

    # Create output directories
    mkdir -p "$OUTPUT_DIR/bams"
    mkdir -p "$OUTPUT_DIR/depth"
    mkdir -p "$OUTPUT_DIR/indexed"

    # Get reference name
    REF_NAME=$(basename "$REFERENCE" .fna)

    # Index reference if not already indexed
    if [ ! -f "$OUTPUT_DIR/indexed/${REF_NAME}.bwt.2bit.64" ]; then
        echo "Indexing reference $REFERENCE..."
        bwa-mem2 index -p "$OUTPUT_DIR/indexed/$REF_NAME" "$REFERENCE"
        touch "$OUTPUT_DIR/indexed/${REF_NAME}.done"
    fi

    # Map reads to reference
    echo "Mapping reads $READS to $REF_NAME..."
    BAM_FILE="$OUTPUT_DIR/bams/${SAMPLE}___${REF_NAME}.sorted.bam"

    # Check if BAM file already exists
    if [ -f "$BAM_FILE" ] && [ -s "$BAM_FILE" ]; then
        echo "BAM file already exists, skipping mapping."
    else
        echo "Running BWA-MEM2 alignment..."
        bwa-mem2 mem -M -p -t "$THREADS" "$OUTPUT_DIR/indexed/$REF_NAME" "$READS" | \
            samtools view -q 10 -bS - | \
            samtools sort -o "$BAM_FILE" -

        # Index BAM file
        echo "Indexing BAM file..."
        samtools index "$BAM_FILE"
    fi

    # Calculate coverage statistics
    DEPTH_FILE="$OUTPUT_DIR/depth/${SAMPLE}_depth.tab"
    if [ ! -f "$DEPTH_FILE" ] || [ ! -s "$DEPTH_FILE" ]; then
        echo "Calculating coverage statistics..."
        python3 /usr/local/bin/get_depth_coverage_single.py \
            --bam "$BAM_FILE" \
            --assembly "$REFERENCE" \
            --sample-name "$SAMPLE" \
            --mapq 10 \
            --paired n \
            --out "$DEPTH_FILE"
    fi

    echo "Mapping completed successfully!"

%labels
    Author Frederik Schulz
    Version 1.0
    Date 2025-04-28
    Description Container for read mapping using bwa-mem2 and samtools

%help
    This container runs bwa-mem2 and samtools (installed via Pixi) for read mapping.

    Usage:
        apptainer run --bind /path/to/reads:/data/input \
                      --bind /path/to/output:/data/output \
                      --bind /path/to/reference:/reference \
                      mapping-debian.sif \
                      --threads <num_threads> \
                      --reads "/data/input/reads.fastq.gz" \
                      --reference "/reference/reference.fna" \
                      --output-dir "/data/output" \
                      --sample "sample_name"

    Arguments:
        --threads    Number of threads to use (default: 1)
        --reads      Path to the reads file (interleaved FASTQ)
        --reference  Path to the reference file (FASTA)
        --output-dir Path to the output directory
        --sample     Sample name (optional, will be derived from reads filename if not provided)

    The results will be stored in the output directory with the following structure:
    - bams/: Contains the sorted BAM files
    - depth/: Contains the depth statistics
    - indexed/: Contains the indexed reference files
