Bootstrap: docker
From: debian:bullseye-slim

%files
    containers/src/assembly_wrapper_pixi.sh /usr/local/bin/assembly_wrapper.sh
    containers/src/filtermultifa_length.py /usr/local/bin/filtermultifa_length.py

%post
    # Install basic dependencies
    apt-get update
    apt-get install -y --no-install-recommends \
        wget \
        ca-certificates \
        curl \
        procps \
        python3 \
        python3-pip \
        git

    # Install Pixi
    cd /opt
    wget https://github.com/prefix-dev/pixi/releases/latest/download/pixi-x86_64-unknown-linux-musl.tar.gz
    mkdir -p pixi_tmp
    tar -xzf pixi-x86_64-unknown-linux-musl.tar.gz -C pixi_tmp
    cp pixi_tmp/pixi /usr/local/bin/
    chmod +x /usr/local/bin/pixi
    rm -rf pixi_tmp pixi-x86_64-unknown-linux-musl.tar.gz

    # Verify pixi installation
    pixi --version

    # Install SPAdes 4.1 globally using pixi
    echo "Installing SPAdes 4.1 globally with Pixi..."
    pixi global install -c conda-forge -c bioconda spades=4.1.0

    # Create symlinks for SPAdes executables
    ln -sf /root/.pixi/envs/spades/bin/spades.py /usr/local/bin/
    ln -sf /root/.pixi/envs/spades/bin/metaspades.py /usr/local/bin/
    ln -sf /root/.pixi/envs/spades/bin/rnaspades.py /usr/local/bin/
    ln -sf /root/.pixi/envs/spades/bin/plasmidspades.py /usr/local/bin/
    ln -sf /root/.pixi/envs/spades/bin/metaviralspades.py /usr/local/bin/
    ln -sf /root/.pixi/envs/spades/bin/metaplasmidspades.py /usr/local/bin/

    # Verify SPAdes installation
    spades.py --version || echo "Warning: SPAdes installation may have issues"

    # Install Python dependencies
    pip3 install --no-cache-dir biopython

    # Make scripts executable
    chmod +x /usr/local/bin/assembly_wrapper.sh
    chmod +x /usr/local/bin/filtermultifa_length.py

    # Create directories
    mkdir -p /data/input /data/output

%environment
    export LC_ALL=C
    export PATH="/usr/local/bin:/root/.pixi/bin:/root/.pixi/envs/spades/bin:$PATH"
    export SHELL=/bin/bash

%runscript
    exec /usr/local/bin/assembly_wrapper.sh "$@"
