# OpDetect-NF: Microbiome Analysis and Pathogen Detection Pipeline

[![Nextflow](https://img.shields.io/badge/nextflow-%E2%89%A524.10.0-brightgreen.svg)](https://www.nextflow.io/)
[![Singularity](https://img.shields.io/badge/singularity-%E2%89%A53.8.0-blue)](https://sylabs.io/singularity/)
[![Pixi](https://img.shields.io/badge/pixi-managed-orange)](https://pixi.sh/)

A comprehensive Nextflow pipeline for microbiome analysis and pathogen detection from sequencing data.

## Overview

OpDetect-NF is a modular, containerized workflow for processing and analyzing microbiome sequencing data with a focus on pathogen detection. The pipeline handles paired-end Illumina sequencing data and performs:

1. Read quality control and preprocessing using BBDuk
2. De novo assembly with SPAdes
3. Viral sequence detection using geNomad and CheckV
4. RNA virus detection using RdRP identification
5. Pathogen mapping and abundance estimation
6. Comprehensive reporting with contig depth information

## Workflow

```mermaid
flowchart TD
    A[Input: Reads] --> B[Read Processing]
    B --> C[Assembly]
    C --> D[Viral Screening]
    D --> D1[geNomad]
    D --> D2[CheckV]
    D --> D3[RdRP Detection]
    D1 --> E[Viral Summary]
    D2 --> E
    D3 --> E
    C --> F[Mapping]
    B --> F
    F --> G[Coverage Analysis]
    F --> H[Pathogen Detection]
    G --> I[Final Report]
    H --> I
    E --> I
```

## Installation

### Prerequisites

- [Nextflow](https://www.nextflow.io/) (≥24.10.0)
- [Singularity](https://sylabs.io/singularity/) (≥3.8.0)
- [Pixi](https://pixi.sh/) for environment management
- At least 16GB RAM and 8 CPU cores
- SLURM cluster (optional, for distributed execution)

### Setup

1. Clone the repository:
   ```bash
   git clone https://github.com/username/opdetect-nf.git
   cd opdetect-nf
   ```

2. Install Pixi if not already installed:
   ```bash
   curl -fsSL https://pixi.sh/install.sh | bash
   ```

3. Initialize the Pixi environment:
   ```bash
   pixi install
   ```

4. Ensure the required database paths are correctly set in `config/database_paths.config`:
   ```
   params {
       rqcfilterdata = "/clusterfs/jgi/scratch/science/mgs/nelli/databases/RQCFilterData"
       genomad_db = "/clusterfs/jgi/scratch/science/mgs/nelli/databases/genomad_db"
       checkv_db = "/clusterfs/jgi/scratch/science/mgs/nelli/databases/checkv-db-v1.5"
       pathogen_db = "/clusterfs/jgi/scratch/science/mgs/nelli/databases/OPdetect/resources/pathdb.fna"
       rdrp_model = "/clusterfs/jgi/scratch/science/mgs/nelli/databases/OPdetect/resources/models/RVMT_All_RdRP_combined_March2022.hmm"
   }
   ```

### Directory Structure

```
opdetect-nf/
├── config/               # Configuration files
│   └── database_paths.config  # Database path configuration
├── modules/              # Nextflow modules
│   ├── assembly.nf       # Assembly module
│   ├── genomad.nf        # geNomad module
│   ├── mapping.nf        # Mapping module
│   ├── pathogen_detection.nf # Pathogen detection module
│   ├── read_processing.nf # Read processing module
│   └── viral_screening.nf # Viral screening module
├── reads/                # Input read files
├── main.nf               # Main workflow
├── nextflow.config       # Nextflow configuration
├── pixi.toml             # Pixi configuration
└── samples.tab           # Sample information
```

## Usage

### Basic Usage

For local execution:
```bash
pixi run nextflow run main.nf
```

For SLURM cluster execution:
```bash
pixi run nextflow run main.nf -profile slurm_pixi
```

To resume a previous run:
```bash
pixi run nextflow run main.nf -profile slurm_pixi --resume
```

### Input Options

The pipeline expects input reads to be in the `reads` directory with sample names specified in `samples.tab`:

1. **Tab-delimited sample file** (required):
   ```
   sample_id
   test1
   test2
   test3
   ```

2. **Read files**:
   - Paired-end reads should be named with `_R1` and `_R2` suffixes before `.fastq.gz`:
     ```
     reads/test1_R1.fastq.gz
     reads/test1_R2.fastq.gz
     ```
   - Already interleaved reads (without these suffixes) will go directly to read filtering with BBDuk:
     ```
     reads/test3.fastq.gz
     ```

### Parameters

| Parameter | Description | Default |
|-----------|-------------|---------|
| `--outdir` | Output directory | `results` |
| `--filter_reads` | Enable additional read filtering | `false` |
| `--dna_min_length` | Minimum contig length for DNA | `800` |
| `--rna_min_length` | Minimum contig length for RNA | `500` |
| `--genomad_min_score` | Minimum score for geNomad | `0.7` |

### Profiles

- `standard`: Local execution with Singularity containers (default)
- `slurm_pixi`: SLURM cluster execution with Pixi-managed Singularity containers

### Resource Requirements

For SLURM execution, the following resources are allocated:

| Process | CPUs | Memory | Time |
|---------|------|--------|------|
| Default | 8 | 16 GB | 1h |
| BBDUK_READS | 8 | 16 GB | 1h |
| GENOMAD_END_TO_END | 8 | 16 GB | 30m |

## Output

The pipeline generates a structured output in the specified output directory:

```
results/
├── 00data/
│   ├── reads/                # Filtered and interleaved reads
│   └── readsf/               # Sample-specific read filtering results
│       └── sample_id/        # Per-sample filtered reads and stats
├── 01assembly/
│   ├── sample_id/            # Per-sample assembly results
│   │   ├── contigs.fasta     # Assembled contigs
│   │   └── scaffolds.fasta   # Assembled scaffolds
│   └── stats/                # Assembly statistics
├── 02mapping/
│   ├── sample_id/            # Per-sample mapping results
│   │   ├── ref/              # Reference sequences
│   │   ├── bams/             # BAM alignment files
│   │   ├── depth/            # Coverage depth files
│   │   └── stats/            # Mapping statistics
│   └── stats/                # Combined mapping statistics
├── 04viral_detection/
│   └── sample_id/            # Per-sample viral detection results
│       ├── genomad/          # geNomad results
│       ├── checkv/           # CheckV results
│       ├── rdrp/             # RdRP detection results
│       └── sample_id_viral_screening_summary.tsv  # Viral summary with contig depth
└── stats/                    # Overall statistics
```

## Test Data

The pipeline comes with test data in the `reads` directory. To run the pipeline with the test data:

```bash
# Run locally
pixi run nextflow run main.nf

# Run on SLURM cluster
pixi run nextflow run main.nf -profile slurm_pixi
```

The pipeline automatically includes contig depth information in the viral screening summary by extracting it from the mapping results.

Note: The test data files are large (>80MB each) and are not included in the repository. You need to place your own test data in the `reads` directory.

## Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Citation

If you use this pipeline in your research, please cite:

```
Author et al. (2023). OpDetect-NF: A Nextflow pipeline for microbiome analysis and pathogen detection.
Journal of Open Source Software, X(XX), XXXX. https://doi.org/10.XXXX/XXXX
```

