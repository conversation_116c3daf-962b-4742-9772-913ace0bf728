[project]
name = "microbiome-pipeline"
version = "0.1.0"
description = "Microbiome analysis pipeline with Nextflow"
authors = ["<PERSON><PERSON><PERSON>"]
channels = ["conda-forge", "bioconda"]
platforms = ["linux-64"]

[dependencies]
python = ">=3.9"
# Read processing tools
bbmap = "*"
seqkit = "*"
# Assembly tools
spades = "*"
biopython = "*"
# Mapping tools
bwa-mem2 = "*"
samtools = "*"
pysam = "*"
# Analysis tools
pandas = "*"
numpy = "*"
# Nextflow
nextflow = "*"
openjdk = ">=22.0.1,<23"

[environments]
default = { features = [] }

[tasks]
# Read processing tasks
interleave = "GENOMAD_DB=/home/<USER>/db/genomad_db CHECKV_DB=/home/<USER>/db/checkv-db-v1.5 reformat.sh"
filter = "GENOMAD_DB=/home/<USER>/db/genomad_db CHECKV_DB=/home/<USER>/db/checkv-db-v1.5 bbduk.sh"
stats = "seqkit stats"
# Assembly tasks
spades = "spades.py"
# Mapping tasks
bwa-mem2 = "bwa-mem2"
samtools = "samtools"
# Pipeline tasks
nextflow = "nextflow"
# Run nextflow with custom arguments
run-nextflow = "nextflow"
# Run nextflow with slurm profile
run-nextflow-slurm = "nextflow run main.nf -profile slurm_pixi"
# Test the read processing
test-reads = "nextflow run test_read_processing.nf"
# Run the full pipeline
run-pipeline = "nextflow run main.nf -profile singularity --input data/samples.csv --outdir results"
# Run the pipeline with reads from reads directory
run-reads = "nextflow run main.nf -profile singularity --reads_dir reads --outdir results"
# Run the pipeline with samples.tab file
run-samples = "nextflow run main.nf -profile singularity --samples_tab samples.tab --reads_dir reads --outdir results"
# Run the pipeline with Slurm (Nextflow runs locally, tasks submitted to Slurm)
run-slurm = "nextflow run main.nf -profile slurm_pixi --samples_tab samples.tab --reads_dir reads --outdir results --enable_containers true"
# Run the pipeline with Pixi and Slurm (recommended approach)
run-pixi-slurm = "./bin/run_nextflow_with_pixi.sh"
# Resume a failed run with Pixi and Slurm
resume-pixi-slurm = "./bin/run_nextflow_with_pixi.sh --resume"
# Resume a failed run and overwrite logs
resume-pixi-slurm-overwrite = "./bin/run_nextflow_with_pixi.sh --resume --overwrite"
