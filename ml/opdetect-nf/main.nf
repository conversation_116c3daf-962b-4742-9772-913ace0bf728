#!/usr/bin/env nextflow

nextflow.enable.dsl = 2

// Import modules
include { READ_PROCESSING } from './modules/read_processing'
include { ASSEMBLY_WORKFLOW } from './modules/assembly'
include { MAPPING_WORKFLOW } from './modules/mapping'
include { GENERATE_MAPPING_STATS } from './modules/mapping'
include { VIRAL_SCREENING_WORKFLOW } from './modules/viral_screening'
include { PATHOGEN_DETECTION_WORKFLOW } from './modules/pathogen_mapping'

// Log info
log.info """\
    M I C R O B I O M E   A N A L Y S I S   P I P E L I N E
    ===================================================
    Input data       : ${params.samples_tab ? "Samples from: ${params.samples_tab}" :
                        params.reads_dir ? "Reads from directory: ${params.reads_dir}" :
                        "CSV file: ${params.input}"}
    Reads directory  : ${params.reads_dir}
    Output directory : ${params.outdir}
    Filter reads     : ${params.filter_reads}

    Database paths:
    geNomad DB       : ${params.genomad_db}
    CheckV DB        : ${params.checkv_db}
    Pathogen DB      : ${params.pathogen_db}
    RdRP model       : ${params.rdrp_model}

    Assembly parameters:
    DNA min length   : ${params.dna_min_length}
    RNA min length   : ${params.rna_min_length}
    """
    .stripIndent()

// Define workflow
workflow {
    // Check if samples_tab parameter is provided
    if (params.samples_tab) {
        // Define input channel from samples.tab file
        // Expected format: sample_id type platform organism
        Channel
            .fromPath(params.samples_tab)
            .splitCsv(header: false, sep: '\t')
            .map { row ->
                def sample_id = row[0]
                def type = row[1]
                def platform = row[2]
                def organism = row[3]

                // Determine if RNA or DNA
                def is_rna = type.toLowerCase() == 'rna'

                // Determine if single-end or paired-end (assuming Illumina is paired-end, ONT is single-end)
                def single_end = platform.toLowerCase() == 'ont'

                // Find read files based on sample_id
                def read_files
                if (single_end) {
                    // For ONT (single-end)
                    read_files = file("${params.reads_dir}/${sample_id}.fastq.gz")
                } else {
                    // For Illumina (paired-end)
                    def r1 = file("${params.reads_dir}/${sample_id}_R1.fastq.gz")
                    def r2 = file("${params.reads_dir}/${sample_id}_R2.fastq.gz")
                    read_files = [r1, r2]
                }

                def meta = [
                    id: sample_id,
                    single_end: single_end,
                    is_rna: is_rna,
                    platform: platform,
                    organism: organism
                ]

                return [meta, read_files, null]  // null for assembly (will be generated)
            }
            .branch {
                has_assembly: it[2] != null
                needs_assembly: it[2] == null
            }
            .set { input_ch }

        log.info "Processing samples from: ${params.samples_tab}"
    }
    // Check if reads_dir parameter is provided
    else if (params.reads_dir) {
        // Define input channel from reads directory
        Channel
            .fromFilePairs("${params.reads_dir}/*_{R1,R2}.fastq.gz", checkIfExists: true)
            .map { id, files ->
                def meta = [
                    id: id,
                    single_end: false,
                    is_rna: id.toLowerCase().contains('rna')
                ]
                return [meta, files, null]  // null for assembly (will be generated)
            }
            .branch {
                has_assembly: it[2] != null
                needs_assembly: it[2] == null
            }
            .set { input_ch }

        log.info "Processing reads from directory: ${params.reads_dir}"
    }
    // Use CSV file as fallback
    else {
        // Define input channel from CSV file
        // Expected format: sample_id,assembly_path,reads_path
        Channel
            .fromPath(params.input)
            .splitCsv(header: true)
            .map { row ->
                def meta = [
                    id: row.sample_id,
                    single_end: row.reads_path.endsWith('.fastq.gz') || row.reads_path.endsWith('.fq.gz'),
                    is_rna: row.sample_id.toLowerCase().contains('rna') || (row.containsKey('type') && row.type.toLowerCase() == 'rna')
                ]
                def reads = meta.single_end ? file(row.reads_path) : [file(row.reads_path.split(';')[0]), file(row.reads_path.split(';')[1])]

                // If assembly_path is provided, use it; otherwise set to null for de novo assembly
                def assembly = row.containsKey('assembly_path') && row.assembly_path ? file(row.assembly_path) : null

                return [meta, reads, assembly]
            }
            .branch {
                has_assembly: it[2] != null
                needs_assembly: it[2] == null
            }
            .set { input_ch }
    }

    // Process reads for samples that need assembly
    read_pairs_ch = input_ch.needs_assembly.map { meta, reads, assembly -> [meta, reads] }
    READ_PROCESSING(read_pairs_ch)

    // Perform assembly for samples that need it (if not skipped)
    if (!params.skip_assembly) {
        ASSEMBLY_WORKFLOW(READ_PROCESSING.out.reads)

        // Combine pre-existing assemblies with newly created ones
        assembly_ch = input_ch.has_assembly
            .map { meta, reads, assembly -> [meta, assembly] }
            .mix(ASSEMBLY_WORKFLOW.out.assembly)
    } else {
        // Use only pre-existing assemblies
        assembly_ch = input_ch.has_assembly
            .map { meta, reads, assembly -> [meta, assembly] }
    }

    // Map reads back to assemblies (if not skipped)
    if (!params.skip_mapping) {
        reads_for_mapping_ch = input_ch.has_assembly
            .map { meta, reads, assembly -> [meta, reads] }
            .mix(READ_PROCESSING.out.reads)

        MAPPING_WORKFLOW(assembly_ch, reads_for_mapping_ch)
    }

    // Run viral screening workflow (if not skipped)
    if (!params.skip_viral_screening) {
        VIRAL_SCREENING_WORKFLOW(assembly_ch)
    }

    // Run pathogen detection workflow (if not skipped)
    if (!params.skip_pathogen_detection && !params.skip_mapping) {
        PATHOGEN_DETECTION_WORKFLOW(
            reads_for_mapping_ch,
            MAPPING_WORKFLOW.out.bam
        )
    }
}

// Workflow completion notification
workflow.onComplete {
    log.info "Pipeline completed at: ${workflow.complete}"
    log.info "Execution status: ${workflow.success ? 'OK' : 'Failed'}"
    log.info "Execution duration: ${workflow.duration}"
}
