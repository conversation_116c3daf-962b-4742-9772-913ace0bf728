// Process configuration for the microbiome analysis pipeline

process {
    // Default process configuration
    cpus = { check_max(1 * task.attempt, 'cpus') }
    memory = { check_max(4.GB * task.attempt, 'memory') }
    time = { check_max(2.h * task.attempt, 'time') }

    errorStrategy = { task.exitStatus in [143,137,104,134,139] ? 'retry' : 'finish' }
    maxRetries = 3
    maxErrors = '-1'

    // Default container settings
    container = 'containers/assembly_pixi.sif'

    withName: 'INTERLEAVE_READS|FILTER_READS|GENERATE_READ_STATS' {
        container = 'containers/readqc.sif'
    }

    withName: 'ASSEMBLY|FILTER_CONTIGS|GENERATE_ASSEMBLY_STATS' {
        container = 'containers/assembly_pixi.sif'
    }

    withName: 'INDEX_ASSEMBLY|MAP_READS|CALCULATE_COVERAGE' {
        container = 'containers/mapping-debian.sif'
        publishDir = [
            path: { "${params.outdir}/02mapping/${meta.id}" },
            mode: 'copy'
        ]
    }

    withName: 'COMBINE_COVERAGE_STATS' {
        container = 'containers/mapping-debian.sif'
        publishDir = [
            path: "${params.outdir}/02mapping",
            mode: 'copy'
        ]
    }

    withName: 'PATHOGEN_MAPPING' {
        container = 'containers/mapping-debian.sif'
        containerOptions = "--bind /home/<USER>/db/OPdetect/resources/pathdb.fna:/databases/pathogen_db:ro"
        publishDir = [
            path: { "${params.outdir}/02mapping/${meta.id}" },
            mode: 'copy'
        ]
    }

    withName: 'PATHOGEN_DETECTION_WORKFLOW:PATHOGEN_MAPPING' {
        container = 'containers/mapping-debian.sif'
        containerOptions = "--bind /home/<USER>/db/OPdetect/resources/pathdb.fna:/databases/pathogen_db:ro"
        publishDir = [
            path: { "${params.outdir}/02mapping/${meta.id}" },
            mode: 'copy'
        ]
    }

    withName: 'PATHOGEN_DETECTION_WORKFLOW:PATHOGEN_DETECTION_SUMMARY' {
        container = 'containers/mapping-debian.sif'
        publishDir = [
            path: { "${params.outdir}/02mapping/${meta.id}" },
            mode: 'copy'
        ]
    }

    // Process-specific configuration for viral screening
    withName: 'VIRAL_SCREENING_WORKFLOW:GENOMAD_END_TO_END' {
        container = 'containers/genomad-debian.sif'
        // Use absolute path for database binding
        containerOptions = "--bind /clusterfs/jgi/scratch/science/mgs/nelli/databases/genomad_db:/databases/genomad_db:ro"
        cpus = { check_max(4 * task.attempt, 'cpus') }
        memory = { check_max(16.GB * task.attempt, 'memory') }
        time = { check_max(30.min * task.attempt, 'time') }
        cache = 'lenient'
        publishDir = [
            path: { "${params.outdir}/04viral_detection/${meta.id}/genomad" },
            mode: 'copy',
            saveAs: { filename ->
                if (filename.endsWith('.err') || filename.endsWith('.out')) null
                else filename
            }
        ]
        // Modify error strategy to retry on specific errors but fail on others
        errorStrategy = { task.exitStatus in [143,137,104,134,139] ? 'retry' : 'terminate' }
        maxRetries = 3
    }

    withName: 'VIRAL_SCREENING_WORKFLOW:CHECKV_END_TO_END' {
        container = 'containers/checkv-debian.sif'
        // Use absolute path for database binding
        containerOptions = "--bind /clusterfs/jgi/scratch/science/mgs/nelli/databases/checkv-db-v1.5:/databases/checkv-db:ro"
        cpus = { check_max(4 * task.attempt, 'cpus') }
        memory = { check_max(8.GB * task.attempt, 'memory') }
        time = { check_max(1.h * task.attempt, 'time') }
        cache = 'lenient'
        publishDir = [
            path: { "${params.outdir}/04viral_detection/${meta.id}/checkv" },
            mode: 'copy'
        ]
    }

    withName: 'VIRAL_SCREENING_WORKFLOW:RDRP_DETECTION' {
        container = 'containers/genomad-debian.sif'
        // Use absolute path for database binding
        containerOptions = "--bind /clusterfs/jgi/scratch/science/mgs/nelli/databases/OPdetect/resources/models/RVMT_All_RdRP_combined_March2022.hmm:/databases/rdrp_model:ro"
        cpus = { check_max(4 * task.attempt, 'cpus') }
        memory = { check_max(8.GB * task.attempt, 'memory') }
        time = { check_max(1.h * task.attempt, 'time') }
        cache = 'lenient'
        publishDir = [
            path: { "${params.outdir}/04viral_detection/${meta.id}/rdrp" },
            mode: 'copy'
        ]
    }

    withName: 'VIRAL_SCREENING_WORKFLOW:VIRAL_SCREENING_SUMMARY' {
        container = 'containers/genomad-debian.sif'
        cpus = 1
        memory = { check_max(2.GB * task.attempt, 'memory') }
        time = { check_max(30.min * task.attempt, 'time') }
        cache = 'lenient'
        publishDir = [
            path: { "${params.outdir}/04viral_detection/${meta.id}" },
            mode: 'copy'
        ]
    }
}

// Function to check if a value is greater than a specified max
def check_max(obj, type) {
    if (type == 'memory') {
        try {
            if (obj.compareTo(params.max_memory as nextflow.util.MemoryUnit) == 1)
                return params.max_memory as nextflow.util.MemoryUnit
            else
                return obj
        } catch (all) {
            println "   ### ERROR ###   Max memory '${params.max_memory}' is not valid! Using default value: $obj"
            return obj
        }
    } else if (type == 'time') {
        try {
            if (obj.compareTo(params.max_time as nextflow.util.Duration) == 1)
                return params.max_time as nextflow.util.Duration
            else
                return obj
        } catch (all) {
            println "   ### ERROR ###   Max time '${params.max_time}' is not valid! Using default value: $obj"
            return obj
        }
    } else if (type == 'cpus') {
        try {
            return Math.min( obj, params.max_cpus as int )
        } catch (all) {
            println "   ### ERROR ###   Max cpus '${params.max_cpus}' is not valid! Using default value: $obj"
            return obj
        }
    }
}
