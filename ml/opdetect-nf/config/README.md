# Configuration Files

This directory contains configuration files for the microbiome analysis pipeline.

## Files

- `database_paths.config`: Contains paths to all required databases and reference files

## Database Paths

The `database_paths.config` file defines the following database paths:

1. **geNomad Database** (`params.genomad_db`): 
   - Default: `${params.db_base}/genomad_db`
   - Used for viral and plasmid detection

2. **CheckV Database** (`params.checkv_db`):
   - Default: `${params.db_base}/checkv-db-v1.5`
   - Used for viral quality assessment

3. **Pathogen Database** (`params.pathogen_db`):
   - Default: `${params.db_base}/OPdetect/resources/pathdb.fna`
   - Used for mapping reads against pathogen references

4. **RdRP Model** (`params.rdrp_model`):
   - Default: `${params.db_base}/OPdetect/resources/models/RVMT_All_RdRP_combined_March2022.hmm`
   - Used for identifying RdRP domains

## Customization

You can modify these paths to match your system by editing the `database_paths.config` file. The paths can be absolute or relative to the user's home directory (using `$HOME` or `~`).

Example:
```nextflow
params {
    // Override default base directory
    db_base = "/data/databases"
    
    // Override specific database path
    genomad_db = "/specific/path/to/genomad_db"
}
```

## Environment Variables

When running in containers, these paths are mounted and made available as environment variables:

- `$GENOMAD_DB`: Path to geNomad database inside container
- `$CHECKV_DB`: Path to CheckV database inside container
- `$PATHOGEN_DB`: Path to pathogen database inside container
- `$RDRP_MODEL`: Path to RdRP model inside container
