// Main Nextflow configuration file

// Include database paths configuration
includeConfig 'config/database_paths.config'

// Include process configuration
includeConfig 'config/process.config'

// Default container settings are defined in process.config

// Executor configuration (adjust based on your environment)
executor {
    name = 'local'
    cpus = 4
    memory = '16 GB'
}

// Profiles configuration
profiles {
    standard {
        // Default profile
        process.executor = 'local'
        process.cpus = 4
        process.memory = '16 GB'

        // Enable Singularity for containers
        singularity.enabled = true
        singularity.autoMounts = true
        singularity.cacheDir = "$HOME/.singularity/cache"

        // Ensure all database paths are correctly bound
        // Use absolute paths for all database bindings to avoid any path resolution issues
        singularity.runOptions = '--bind /clusterfs --bind /clusterfs/jgi/scratch/science/mgs/nelli/databases/RQCFilterData:/databases/RQCFilterData:ro --bind /clusterfs/jgi/scratch/science/mgs/nelli/databases/genomad_db:/databases/genomad_db:ro --bind /clusterfs/jgi/scratch/science/mgs/nelli/databases/checkv-db-v1.5:/databases/checkv-db:ro --bind /clusterfs/jgi/scratch/science/mgs/nelli/databases/OPdetect/resources/pathdb.fna:/databases/pathogen_db:ro --bind /clusterfs/jgi/scratch/science/mgs/nelli/databases/OPdetect/resources/models/RVMT_All_RdRP_combined_March2022.hmm:/databases/rdrp_model:ro'
    }

    singularity {
        singularity.enabled = true
        singularity.autoMounts = true
        singularity.cacheDir = "$HOME/.singularity/cache"
        docker.enabled = false
    }

    docker {
        docker.enabled = true
        docker.temp = 'auto'
        singularity.enabled = false
    }

    // Profile for Slurm with Pixi containers
    slurm_pixi {
        // Set Slurm as the executor for all processes
        process.executor = 'slurm'

        // Slurm submission parameters
        process.queue = 'jgi_normal'
        process.account = 'grp-org-sc-mgs'
        process.clusterOptions = '#SBATCH -J opdetect-nf'

        // Default resources for all tasks
        process.cpus = 8
        process.memory = '16 GB'
        process.time = '1h'

        // Specific resources for BBDuk
        withName: 'BBDUK_READS' {
            cpus = 8
            memory = '16 GB'
            time = '1h'
        }

        // Specific resources for geNomad
        withName: 'GENOMAD_END_TO_END' {
            cpus = 8
            memory = '16 GB'
            time = '30m'
        }

        // Enable Singularity for containers
        singularity.enabled = true
        singularity.autoMounts = true
        singularity.cacheDir = "$HOME/.singularity/cache"

        // Ensure all database paths are correctly bound
        // Use absolute paths for all database bindings to avoid any path resolution issues
        singularity.runOptions = '--bind /clusterfs --bind /clusterfs/jgi/scratch/science/mgs/nelli/databases/RQCFilterData:/databases/RQCFilterData:ro --bind /clusterfs/jgi/scratch/science/mgs/nelli/databases/genomad_db:/databases/genomad_db:ro --bind /clusterfs/jgi/scratch/science/mgs/nelli/databases/checkv-db-v1.5:/databases/checkv-db:ro --bind /clusterfs/jgi/scratch/science/mgs/nelli/databases/OPdetect/resources/pathdb.fna:/databases/pathogen_db:ro --bind /clusterfs/jgi/scratch/science/mgs/nelli/databases/OPdetect/resources/models/RVMT_All_RdRP_combined_March2022.hmm:/databases/rdrp_model:ro'

        // Container settings
        params.enable_containers = true
        params.container_dir = "${baseDir}/containers"

        // Error handling for Slurm jobs
        process.errorStrategy = { task.exitStatus in [143,137,104,134,139] ? 'retry' : 'finish' }
        process.maxRetries = 3

        // Use scratch directory for temporary files
        process.scratch = true
    }
}

// Singularity configuration (default)
singularity {
    enabled = true
    autoMounts = true
    cacheDir = "$HOME/.singularity/cache"
    pullTimeout = '60 min'
    runOptions = '--no-home'
    // Use local SIF files, don't pull from Docker Hub
    pull = false
}

// Docker configuration (disabled by default)
docker {
    enabled = false
    // Remove containers after use
    temp = 'auto'
}

// Environment variables that will be passed to the containers
env {
    RQCFILTERDATA = "/databases/RQCFilterData"
    GENOMAD_DB = "/databases/genomad_db"
    CHECKV_DB = "/databases/checkv-db"
    PATHOGEN_DB = "/databases/pathogen_db"
    RDRP_MODEL = "/databases/rdrp_model"
}

// Workflow parameters
params {
    // Input/output parameters
    input = "data/samples.csv"
    outdir = "results"
    reads_dir = "reads"  // Directory containing paired-end reads
    samples_tab = "samples.tab"  // Tab-delimited file with sample information

    // Read processing parameters
    filter_reads = false

    // Assembly parameters
    dna_min_length = 800
    rna_min_length = 500

    // Viral detection parameters
    genomad_min_score = 0.7
    ani_threshold = 95

    // Module selection
    skip_assembly = false
    skip_viral_screening = false
    skip_mapping = false
    skip_pathogen_detection = false

    // Resource parameters
    max_cpus = 4
    max_memory = '16 GB'
    max_time = '24h'
}
