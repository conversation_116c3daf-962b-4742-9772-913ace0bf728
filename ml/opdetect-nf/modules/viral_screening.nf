// Viral screening workflow module for Nextflow pipeline

include { GENOMAD_END_TO_END } from './genomad'
include { CHECKV_END_TO_END } from './checkv'
include { RDRP_DETECTION } from './rdrp_detection'

// Process to generate a summary of viral screening results
process VIRAL_SCREENING_SUMMARY {
    tag "${meta.id}"
    publishDir "${params.outdir}/04viral_detection/${meta.id}", mode: 'copy'

    container 'containers/genomad-debian.sif'

    cpus 1
    memory 2.GB

    input:
    tuple val(meta), path(virus_fasta), path(virus_summary), path(quality_summary), path(rdrp_hits)

    output:
    tuple val(meta), path("${meta.id}_viral_screening_summary.tsv"), emit: summary
    path "versions.yml", emit: versions

    script:
    def prefix = task.ext.prefix ?: "${meta.id}"

    """
    # Create a summary file
    echo -e "sample_id\\tcontig_id\\tlength\\tgenomic_type\\tviral_score\\ttaxonomy\\tcheckv_quality\\tcompleteness\\trdrp_hit\\tcontig_depth" > ${prefix}_viral_screening_summary.tsv

    # Get contig depth information from the mapping results
    if [ -f "${params.outdir}/02mapping/${meta.id}/stats/mapping_stats_self.tsv" ]; then
        grep "${meta.id}" "${params.outdir}/02mapping/${meta.id}/stats/mapping_stats_self.tsv" > contig_depths.tmp
    else
        touch contig_depths.tmp
    fi

    # Process geNomad virus summary
    if [ -s "${virus_summary}" ]; then
        # Check if the virus summary has content (not just a header)
        if [ `wc -l < ${virus_summary}` -gt 1 ]; then
            # Skip header line
            tail -n +2 ${virus_summary} | while read line; do
                contig_id=\$(echo "\$line" | cut -f1)
                genomic_type=\$(echo "\$line" | cut -f2)
                length=\$(echo "\$line" | cut -f3)
                viral_score=\$(echo "\$line" | cut -f5)
                taxonomy=\$(echo "\$line" | cut -f9)

                # Default values for CheckV and RdRP
                checkv_quality="NA"
                completeness="NA"
                rdrp_hit="No"
                contig_depth="NA"

                # Check if contig is in CheckV quality summary
                if [ -s "${quality_summary}" ]; then
                    checkv_info=\$(grep -w "\$contig_id" ${quality_summary} || echo "")
                    if [ ! -z "\$checkv_info" ]; then
                        checkv_quality=\$(echo "\$checkv_info" | cut -f8)
                        completeness=\$(echo "\$checkv_info" | cut -f6)
                    fi
                fi

                # Check if contig has RdRP hit
                if [ -s "${rdrp_hits}" ]; then
                    if grep -q "\$contig_id" ${rdrp_hits}; then
                        rdrp_hit="Yes"
                    fi
                fi

                # Get contig depth from mapping results
                if [ -f "${params.outdir}/02mapping/${meta.id}/stats/mapping_stats_self.tsv" ]; then
                    # Try to match the exact contig ID first
                    depth_info=\$(grep -w "\$contig_id" "${params.outdir}/02mapping/${meta.id}/stats/mapping_stats_self.tsv" || echo "")
                    if [ ! -z "\$depth_info" ]; then
                        # Mean depth is in column 6
                        contig_depth=\$(echo "\$depth_info" | cut -f6)
                        echo "Found depth for exact match \$contig_id: \$contig_depth"
                    else
                        # If exact match fails, try to match by the NODE_X part
                        contig_base=\$(echo "\$contig_id" | grep -o "NODE_[0-9]*")
                        if [ ! -z "\$contig_base" ]; then
                            echo "Looking for depth for contig base: \$contig_base"
                            depth_info=\$(grep "\$contig_base" "${params.outdir}/02mapping/${meta.id}/stats/mapping_stats_self.tsv" || echo "")
                            if [ ! -z "\$depth_info" ]; then
                                # Mean depth is in column 6
                                contig_depth=\$(echo "\$depth_info" | cut -f6)
                                echo "Found depth for \$contig_id (\$contig_base): \$contig_depth"
                            else
                                echo "No depth found for \$contig_id (\$contig_base)"
                                # Try one more approach - extract just the NODE_X_length_Y part
                                contig_length=\$(echo "\$contig_id" | grep -o "NODE_[0-9]*_length_[0-9]*")
                                if [ ! -z "\$contig_length" ]; then
                                    echo "Looking for depth for contig with length: \$contig_length"
                                    depth_info=\$(grep "\$contig_length" "${params.outdir}/02mapping/${meta.id}/stats/mapping_stats_self.tsv" || echo "")
                                    if [ ! -z "\$depth_info" ]; then
                                        contig_depth=\$(echo "\$depth_info" | cut -f6)
                                        echo "Found depth for \$contig_id (\$contig_length): \$contig_depth"
                                    else
                                        echo "No depth found for \$contig_id (\$contig_length)"
                                        contig_depth="NA"
                                    fi
                                else
                                    contig_depth="NA"
                                fi
                            fi
                        else
                            echo "Could not extract NODE_X pattern from contig ID: \$contig_id"
                            contig_depth="NA"
                        fi
                    fi
                else
                    echo "Mapping stats file not found: ${params.outdir}/02mapping/${meta.id}/stats/mapping_stats_self.tsv"
                    contig_depth="NA"
                fi

                # Write to summary file
                echo -e "${meta.id}\\t\$contig_id\\t\$length\\t\$genomic_type\\t\$viral_score\\t\$taxonomy\\t\$checkv_quality\\t\$completeness\\t\$rdrp_hit\\t\$contig_depth" >> ${prefix}_viral_screening_summary.tsv.tmp
            done
        else
            # No viruses found, create an empty summary with just the header
            echo "No viruses found in sample ${meta.id}"
            echo -e "${meta.id}\\tNo_viruses_found\\tNA\\tNA\\tNA\\tNA\\tNA\\tNA\\tNo\\tNA" >> ${prefix}_viral_screening_summary.tsv.tmp
        fi
    else
        # Virus summary file doesn't exist or is empty
        echo "No virus summary file found for sample ${meta.id}"
        echo -e "${meta.id}\\tNo_viruses_found\\tNA\\tNA\\tNA\\tNA\\tNA\\tNA\\tNo\\tNA" >> ${prefix}_viral_screening_summary.tsv.tmp
    fi

    # Fix the formatting issues by processing the temporary file
    if [ -f "${prefix}_viral_screening_summary.tsv.tmp" ]; then
        # Create the header
        echo -e "sample_id\\tcontig_id\\tlength\\tgenomic_type\\tviral_score\\ttaxonomy\\tcheckv_quality\\tcompleteness\\trdrp_hit\\tcontig_depth" > ${prefix}_viral_screening_summary.tsv

        # Get the genomad taxonomy information from the original virus_summary file
        echo "Getting taxonomy information from geNomad..."
        # Use cut to extract the taxonomy field (column 11)
        tail -n +2 ${virus_summary} | cut -f1,11 > taxonomy.tmp

        # Check if the mapping stats file exists
        echo "Checking for mapping stats file..."
        if [ -f "${params.outdir}/02mapping/${meta.id}/stats/mapping_stats_self.tsv" ]; then
            echo "Found mapping stats file: ${params.outdir}/02mapping/${meta.id}/stats/mapping_stats_self.tsv"

            # Print the header of the mapping stats file for reference
            echo "Header of mapping stats file:"
            head -n 1 "${params.outdir}/02mapping/${meta.id}/stats/mapping_stats_self.tsv"

            # Print a few lines of the mapping stats file for reference
            echo "First few lines of mapping stats file:"
            head -n 5 "${params.outdir}/02mapping/${meta.id}/stats/mapping_stats_self.tsv"
        else
            echo "Mapping stats file not found: ${params.outdir}/02mapping/${meta.id}/stats/mapping_stats_self.tsv"
        fi

        # Process each line to ensure proper column separation
        tail -n +2 ${prefix}_viral_screening_summary.tsv.tmp | while read line; do
            # Extract fields
            sample=\$(echo "\$line" | cut -f1)
            contig=\$(echo "\$line" | cut -f2)
            length_type=\$(echo "\$line" | cut -f3)

            # Split the length_type field into length and genomic_type
            if [[ "\$length_type" == *"No terminal repeats"* ]]; then
                genomic_type="No terminal repeats"
                length=\$(echo "\$length_type" | sed 's/No terminal repeats//')
            elif [[ "\$length_type" == *"Direct terminal repeats"* ]]; then
                genomic_type="Direct terminal repeats"
                length=\$(echo "\$length_type" | sed 's/Direct terminal repeats//')
            elif [[ "\$length_type" == *"Inverted terminal repeats"* ]]; then
                genomic_type="Inverted terminal repeats"
                length=\$(echo "\$length_type" | sed 's/Inverted terminal repeats//')
            else
                genomic_type="Unknown"
                length=\$length_type
            fi

            # Get the rest of the fields
            viral_score=\$(echo "\$line" | cut -f4)
            taxonomy=\$(echo "\$line" | cut -f5)
            checkv_quality=\$(echo "\$line" | cut -f6)
            completeness=\$(echo "\$line" | cut -f7)
            rdrp_hit=\$(echo "\$line" | cut -f8)
            contig_depth=\$(echo "\$line" | cut -f9)

            # Get taxonomy from geNomad
            if [ -f "taxonomy.tmp" ]; then
                taxonomy_info=\$(grep -w "\$contig" taxonomy.tmp | cut -f2)
                if [ ! -z "\$taxonomy_info" ]; then
                    taxonomy=\$taxonomy_info
                fi
            fi

            # Get contig depth from mapping results
            if [ -f "${params.outdir}/02mapping/${meta.id}/stats/mapping_stats_self.tsv" ]; then
                # Try to match the exact contig ID first
                depth_info=\$(grep -w "\$contig" "${params.outdir}/02mapping/${meta.id}/stats/mapping_stats_self.tsv" || echo "")
                if [ ! -z "\$depth_info" ]; then
                    # Mean depth is in column 6
                    contig_depth=\$(echo "\$depth_info" | cut -f6)
                    echo "Found depth for exact match \$contig: \$contig_depth"
                else
                    # If exact match fails, try to match by the NODE_X part
                    contig_base=\$(echo "\$contig" | grep -o "NODE_[0-9]*")
                    if [ ! -z "\$contig_base" ]; then
                        echo "Looking for depth for contig base: \$contig_base"
                        depth_info=\$(grep "\$contig_base" "${params.outdir}/02mapping/${meta.id}/stats/mapping_stats_self.tsv" || echo "")
                        if [ ! -z "\$depth_info" ]; then
                            contig_depth=\$(echo "\$depth_info" | cut -f6)
                            echo "Found depth for \$contig (\$contig_base): \$contig_depth"
                        else
                            echo "No depth found for \$contig (\$contig_base)"
                            # Try one more approach - extract just the NODE_X_length_Y part
                            contig_length=\$(echo "\$contig" | grep -o "NODE_[0-9]*_length_[0-9]*")
                            if [ ! -z "\$contig_length" ]; then
                                echo "Looking for depth for contig with length: \$contig_length"
                                depth_info=\$(grep "\$contig_length" "${params.outdir}/02mapping/${meta.id}/stats/mapping_stats_self.tsv" || echo "")
                                if [ ! -z "\$depth_info" ]; then
                                    contig_depth=\$(echo "\$depth_info" | cut -f6)
                                    echo "Found depth for \$contig (\$contig_length): \$contig_depth"
                                else
                                    echo "No depth found for \$contig (\$contig_length)"
                                    contig_depth="NA"
                                fi
                            else
                                contig_depth="NA"
                            fi
                        fi
                    else
                        echo "Could not extract NODE_X pattern from contig ID: \$contig"
                        contig_depth="NA"
                    fi
                fi
            else
                echo "Mapping stats file not found: ${params.outdir}/02mapping/${meta.id}/stats/mapping_stats_self.tsv"
                contig_depth="NA"
            fi

            # Debug: Print the contig depth that will be used
            echo "Final contig depth for \$contig: \$contig_depth"

            # Write to final file with proper formatting
            echo -e "\$sample\\t\$contig\\t\$length\\t\$genomic_type\\t\$viral_score\\t\$taxonomy\\t\$checkv_quality\\t\$completeness\\t\$rdrp_hit\\t\$contig_depth" >> ${prefix}_viral_screening_summary.tsv
        done

        # Remove temporary files
        rm -f ${prefix}_viral_screening_summary.tsv.tmp taxonomy.tmp depth.tmp

        # Post-processing: Double-check that all contigs have depth information
        echo "Post-processing: Ensuring all contigs have depth information..."

        # Create a temporary file for the updated summary
        cp ${prefix}_viral_screening_summary.tsv ${prefix}_viral_screening_summary.tsv.updated

        # Add header to the new file
        head -n 1 ${prefix}_viral_screening_summary.tsv > ${prefix}_viral_screening_summary.tsv.new

        # Process each line in the summary file (skip header)
        tail -n +2 ${prefix}_viral_screening_summary.tsv.updated | while read -r line; do
            # Extract fields
            sample=\$(echo "\$line" | cut -f1)
            contig=\$(echo "\$line" | cut -f2)
            length=\$(echo "\$line" | cut -f3)
            genomic_type=\$(echo "\$line" | cut -f4)
            viral_score=\$(echo "\$line" | cut -f5)
            taxonomy=\$(echo "\$line" | cut -f6)
            checkv_quality=\$(echo "\$line" | cut -f7)
            completeness=\$(echo "\$line" | cut -f8)
            rdrp_hit=\$(echo "\$line" | cut -f9)
            contig_depth=\$(echo "\$line" | cut -f10)

            # If contig_depth is empty, NA, or No, try to get it from the mapping stats file
            if [ -z "\$contig_depth" ] || [ "\$contig_depth" == "NA" ] || [ "\$contig_depth" == "No" ]; then
                if [ -f "${params.outdir}/02mapping/${meta.id}/stats/mapping_stats_self.tsv" ]; then
                    # Try to match the exact contig ID first
                    depth_info=\$(grep -w "\$contig" "${params.outdir}/02mapping/${meta.id}/stats/mapping_stats_self.tsv" || echo "")
                    if [ ! -z "\$depth_info" ]; then
                        # Mean depth is in column 6
                        contig_depth=\$(echo "\$depth_info" | cut -f6)
                        echo "Post-processing: Found depth for exact match \$contig: \$contig_depth"
                    else
                        # If exact match fails, try to match by the NODE_X part
                        contig_base=\$(echo "\$contig" | grep -o "NODE_[0-9]*")
                        if [ ! -z "\$contig_base" ]; then
                            depth_info=\$(grep "\$contig_base" "${params.outdir}/02mapping/${meta.id}/stats/mapping_stats_self.tsv" || echo "")
                            if [ ! -z "\$depth_info" ]; then
                                contig_depth=\$(echo "\$depth_info" | cut -f6)
                                echo "Post-processing: Found depth for \$contig (\$contig_base): \$contig_depth"
                            else
                                # Try one more approach - extract just the NODE_X_length_Y part
                                contig_length=\$(echo "\$contig" | grep -o "NODE_[0-9]*_length_[0-9]*")
                                if [ ! -z "\$contig_length" ]; then
                                    depth_info=\$(grep "\$contig_length" "${params.outdir}/02mapping/${meta.id}/stats/mapping_stats_self.tsv" || echo "")
                                    if [ ! -z "\$depth_info" ]; then
                                        contig_depth=\$(echo "\$depth_info" | cut -f6)
                                        echo "Post-processing: Found depth for \$contig (\$contig_length): \$contig_depth"
                                    fi
                                fi
                            fi
                        fi
                    fi
                fi
            fi

            # If contig_depth is still empty, NA, or No, set it to NA
            if [ -z "\$contig_depth" ] || [ "\$contig_depth" == "No" ]; then
                contig_depth="NA"
            fi

            # Write to the new file with the updated contig depth
            echo -e "\$sample\\t\$contig\\t\$length\\t\$genomic_type\\t\$viral_score\\t\$taxonomy\\t\$checkv_quality\\t\$completeness\\t\$rdrp_hit\\t\$contig_depth" >> ${prefix}_viral_screening_summary.tsv.new
        done

        # Replace the original file with the updated one
        mv ${prefix}_viral_screening_summary.tsv.new ${prefix}_viral_screening_summary.tsv
        rm -f ${prefix}_viral_screening_summary.tsv.updated

        echo "Post-processing complete: Viral screening summary updated with contig depth information."
    fi

    cat <<-END_VERSIONS > versions.yml
    "${task.process}":
        bash: \$(bash --version | head -n 1 | sed 's/.*version //; s/ .*//')
    END_VERSIONS
    """
}



// Workflow for viral screening
workflow VIRAL_SCREENING_WORKFLOW {
    take:
    assembly_ch // Channel with [meta, assembly]

    main:
    // Run geNomad on assemblies
    GENOMAD_END_TO_END(assembly_ch)

    // Create empty files for samples where geNomad failed
    def empty_fasta_ch = GENOMAD_END_TO_END.out.no_viruses
        .map { meta, no_viruses ->
            def empty_virus_fasta = file("${workDir}/empty_${meta.id}_virus.fna")
            empty_virus_fasta.text = ""
            return [meta, empty_virus_fasta]
        }

    def empty_summary_ch = GENOMAD_END_TO_END.out.no_viruses
        .map { meta, no_viruses ->
            def empty_virus_summary = file("${workDir}/empty_${meta.id}_virus_summary.tsv")
            empty_virus_summary.text = "contig_id\tgenomic_type\tlength\tnum_genes\tviral_score\ttaxonomy\n"
            return [meta, empty_virus_summary]
        }

    def empty_proteins_ch = GENOMAD_END_TO_END.out.no_viruses
        .map { meta, no_viruses ->
            def empty_virus_proteins = file("${workDir}/empty_${meta.id}_virus_proteins.faa")
            empty_virus_proteins.text = ""
            return [meta, empty_virus_proteins]
        }

    // Combine geNomad results with empty files for failed samples
    def combined_fasta_ch = GENOMAD_END_TO_END.out.virus_fasta.mix(empty_fasta_ch)
    def combined_summary_ch = GENOMAD_END_TO_END.out.virus_summary.mix(empty_summary_ch)
    def combined_proteins_ch = GENOMAD_END_TO_END.out.virus_proteins.mix(empty_proteins_ch)

    // Run CheckV on viral contigs from geNomad
    CHECKV_END_TO_END(combined_fasta_ch)

    // Run RdRP detection on viral proteins from geNomad
    RDRP_DETECTION(combined_proteins_ch)

    // Create a channel with all the outputs needed for the summary
    // Use join to ensure proper matching of outputs by meta.id
    def summary_inputs = combined_fasta_ch
        .join(combined_summary_ch)
        .join(CHECKV_END_TO_END.out.quality_summary)
        .join(RDRP_DETECTION.out.rdrp_contigs)

    // Generate a summary of viral screening results
    VIRAL_SCREENING_SUMMARY(
        summary_inputs.map { meta, virus_fasta, virus_summary, quality_summary, rdrp_contigs ->
            [meta, virus_fasta, virus_summary, quality_summary, rdrp_contigs]
        }
    )

    emit:
    virus_fasta = combined_fasta_ch
    virus_summary = combined_summary_ch
    checkv_quality = CHECKV_END_TO_END.out.quality_summary
    rdrp_hits = RDRP_DETECTION.out.rdrp_contigs
    summary = VIRAL_SCREENING_SUMMARY.out.summary
}
