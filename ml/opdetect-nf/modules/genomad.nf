// geNomad module for Nextflow pipeline

process GENOMAD_END_TO_END {
    tag "${meta.id}"
    publishDir "${params.outdir}/03viral_detection/${meta.id}/genomad", mode: 'copy'

    container 'containers/genomad-debian.sif'

    cpus 8
    memory 16.GB

    input:
    tuple val(meta), path(fasta)

    output:
    tuple val(meta), path("${meta.id}_summary/${meta.id}_virus.fna"), emit: virus_fasta
    tuple val(meta), path("${meta.id}_summary/${meta.id}_virus_summary.tsv"), emit: virus_summary
    tuple val(meta), path("${meta.id}_summary/${meta.id}_plasmid.fna"), emit: plasmid_fasta
    tuple val(meta), path("${meta.id}_summary/${meta.id}_plasmid_summary.tsv"), emit: plasmid_summary
    tuple val(meta), path("${meta.id}_summary/${meta.id}_virus_proteins.faa"), emit: virus_proteins
    tuple val(meta), path("${meta.id}_genes"), optional: true, emit: genes_dir
    tuple val(meta), path("no_viruses_flag"), optional: true, emit: no_viruses
    path "versions.yml", emit: versions

    script:
    def prefix = meta.id
    def min_score = 0.7
    """
    # Run geNomad end-to-end with error handling
    set +e
    genomad end-to-end \\
        --cleanup \\
        --splits 8 \\
        --threads ${task.cpus} \\
        --min-score ${min_score} \\
        ${fasta} \\
        ./ \\
        ${params.genomad_db}
    genomad_exit_code=\$?
    set -e

    echo "geNomad exit code: \$genomad_exit_code"

    # Create necessary directories and files if geNomad failed
    if [ \$genomad_exit_code -ne 0 ]; then
        echo "geNomad failed with exit code \$genomad_exit_code"
        echo "Creating empty output files due to geNomad failure"

        # Create summary directory if it doesn't exist
        mkdir -p ${prefix}_summary

        # Create empty virus files
        touch ${prefix}_summary/${prefix}_virus.fna
        touch ${prefix}_summary/${prefix}_virus_summary.tsv
        echo "contig_id\tgenomic_type\tlength\tnum_genes\tviral_score\ttaxonomy" > ${prefix}_summary/${prefix}_virus_summary.tsv

        # Create empty plasmid files
        touch ${prefix}_summary/${prefix}_plasmid.fna
        touch ${prefix}_summary/${prefix}_plasmid_summary.tsv
        echo "contig_id\tgenomic_type\tlength\tnum_genes\tplasmid_score\ttaxonomy" > ${prefix}_summary/${prefix}_plasmid_summary.tsv

        # Create empty proteins file
        touch ${prefix}_summary/${prefix}_virus_proteins.faa

        # Create no_viruses flag
        echo "${prefix}: No viruses found (geNomad failed)" > no_viruses_flag

        # Create versions.yml file
        cat > versions.yml << EOL
        "${task.process}":
            genomad: "failed"
EOL

        # Exit with success to allow pipeline to continue
        exit 0
    fi

    # Check if virus summary file exists and has content (beyond header)
    if [ -f "${prefix}_summary/${prefix}_virus_summary.tsv" ] && [ `wc -l < ${prefix}_summary/${prefix}_virus_summary.tsv` -gt 1 ]; then
        # Viruses found, do nothing
        echo "Viruses found in sample ${prefix}"
    else
        # No viruses found, create a flag file
        echo "${prefix}: No viruses found" > no_viruses_flag

        # Create empty files if they don't exist
        mkdir -p ${prefix}_summary
        touch ${prefix}_summary/${prefix}_virus.fna

        if [ ! -f "${prefix}_summary/${prefix}_virus_summary.tsv" ]; then
            echo "contig_id\tgenomic_type\tlength\tnum_genes\tviral_score\ttaxonomy" > ${prefix}_summary/${prefix}_virus_summary.tsv
        fi

        if [ ! -f "${prefix}_summary/${prefix}_virus_proteins.faa" ]; then
            touch ${prefix}_summary/${prefix}_virus_proteins.faa
        fi
    fi

    # Create a symlink to the virus proteins file for easier access
    if [ -f "${prefix}_find_proviruses/${prefix}_proteins.faa" ]; then
        ln -sf ${prefix}_find_proviruses/${prefix}_proteins.faa ${prefix}_summary/${prefix}_virus_proteins.faa
    elif [ ! -f "${prefix}_summary/${prefix}_virus_proteins.faa" ]; then
        # Create an empty file if the proteins file doesn't exist
        touch ${prefix}_summary/${prefix}_virus_proteins.faa
    fi

    cat > versions.yml << EOL
    "${task.process}":
        genomad: \$(genomad --version 2>&1 | sed 's/geNomad version: //g' || echo "unknown")
EOL
    """
}
