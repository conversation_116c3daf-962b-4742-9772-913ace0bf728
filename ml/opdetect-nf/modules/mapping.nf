#!/usr/bin/env nextflow

// Mapping module for microbiome analysis pipeline
// Maps reads back to assemblies to calculate coverage statistics

process INDEX_ASSEMBLY {
    tag "${meta.id}"

    container 'mapping-debian.sif'

    input:
    tuple val(meta), path(assembly)

    output:
    tuple val(meta), path("ref/${meta.id}.fna"), path("ref/${meta.id}.fna.{amb,ann,bwt,pac,sa,bwt.2bit.64,0123}"), emit: index

    script:
    """
    # Create reference directory
    mkdir -p ref

    # Copy assembly to reference directory with standardized name
    cp ${assembly} ref/${meta.id}.fna

    # Index assembly with BWA-MEM2
    bwa-mem2 index ref/${meta.id}.fna

    # Create a dummy .0123 file that BWA-MEM2 expects
    touch ref/${meta.id}.fna.0123
    """
}

process MAP_READS {
    tag "${meta.id}"
    publishDir "${params.outdir}/02mapping/${meta.id}", mode: 'copy'

    container 'mapping-debian.sif'

    cpus 4
    memory 8.GB

    errorStrategy { task.exitStatus in [137,140] ? 'retry' : 'finish' }
    maxRetries 3

    input:
    tuple val(meta), path(assembly), path(index), path(reads)

    output:
    tuple val(meta), path("bams/${meta.id}___${meta.id}.sorted.bam"), path("bams/${meta.id}___${meta.id}.sorted.bam.bai"), emit: bam

    script:
    """
    # Create bams directory
    mkdir -p bams

    # Map reads to assembly using BWA-MEM2
    # The index files should already be in the same directory as the assembly

    # Create ref directory if it doesn't exist
    mkdir -p ref

    # Map reads to assembly
    bwa-mem2 mem -t ${task.cpus} ${assembly} ${reads} | \
        samtools view -bS - | \
        samtools sort -@ ${task.cpus} -o bams/${meta.id}___${meta.id}.sorted.bam -

    # Index BAM file
    samtools index bams/${meta.id}___${meta.id}.sorted.bam
    """
}

process CALCULATE_COVERAGE {
    tag "${meta.id}"
    publishDir "${params.outdir}/02mapping/${meta.id}", mode: 'copy'

    container 'mapping-debian.sif'

    input:
    tuple val(meta), path(bam), path(bai)

    output:
    tuple val(meta), path("depth/${meta.id}_depth.tab"), emit: depth

    script:
    """
    # Create depth directory
    mkdir -p depth

    # Calculate per-base coverage using samtools depth
    samtools depth -a ${bam} > depth/${meta.id}_depth.tab
    """
}

process COMBINE_COVERAGE_STATS {
    publishDir "${params.outdir}/02mapping", mode: 'copy'

    container 'mapping-debian.sif'

    input:
    path(depth_files)

    output:
    path "stats/contig_stats.tab", emit: combined_stats

    script:
    """
    #!/usr/bin/env python3
    import os
    import pandas as pd
    import numpy as np

    # Create stats directory
    os.makedirs('stats', exist_ok=True)

    # Create output file with header
    with open('stats/contig_stats.tab', 'w') as f:
        f.write('contig\\tmean_depth\\tmedian_depth\\tmin_depth\\tmax_depth\\ttotal_depth\\tlength\\tsample\\n')

    # Process each depth file
    depth_files = "${depth_files}".split()

    for depth_file in depth_files:
        sample_id = os.path.basename(depth_file).replace('_depth.tab', '')

        # Read depth file
        try:
            df = pd.read_csv(depth_file, sep='\\t', header=None, names=['contig', 'pos', 'depth'])

            # If file is empty or has no data, continue to next file
            if df.empty:
                continue

            # Group by contig and calculate statistics
            contig_stats = df.groupby('contig').agg({
                'depth': ['mean', 'median', 'min', 'max', 'sum'],
                'pos': 'count'  # Count positions to get length
            }).reset_index()

            # Flatten column names
            contig_stats.columns = ['contig', 'mean_depth', 'median_depth', 'min_depth', 'max_depth', 'total_depth', 'length']

            # Add sample ID
            contig_stats['sample'] = sample_id

            # Write to output file
            contig_stats.to_csv('stats/contig_stats.tab', sep='\\t', index=False, header=False, mode='a')
        except Exception as e:
            # If there's an error, write a message to the output file
            with open('stats/contig_stats.tab', 'a') as f:
                f.write(f"# Error processing {depth_file}: {str(e)}\\n")
    """
}

process GENERATE_MAPPING_STATS {
    publishDir "${params.outdir}/02mapping/${meta.id}", mode: 'copy'

    container 'mapping-debian.sif'

    cpus 1
    memory 4.GB

    input:
    tuple val(meta), path(self_bam), path(self_bai)
    tuple val(meta), path(pathogen_bam), path(pathogen_bai)

    output:
    path("stats/mapping_stats_self.tsv"), emit: self_stats
    path("stats/mapping_stats_pathdb.tsv"), emit: pathogen_stats

    script:
    def prefix = task.ext.prefix ?: "${meta.id}"

    """
    # Create stats directory
    mkdir -p stats

    # Generate mapping statistics
    bash /usr/local/bin/generate_mapping_stats.sh \\
        ${self_bam} \\
        ${pathogen_bam} \\
        stats \\
        ${prefix}
    """
}

// Workflow to map reads to assemblies
workflow MAPPING_WORKFLOW {
    take:
    assembly_ch // Channel with [meta, assembly]
    reads_ch    // Channel with [meta, reads]

    main:
    // Index assembly
    INDEX_ASSEMBLY(assembly_ch)

    // Combine assembly with reads for mapping
    assembly_reads_ch = INDEX_ASSEMBLY.out.index
        .join(reads_ch, by: [0]) // Join by meta

    // Map reads to assembly
    MAP_READS(assembly_reads_ch)

    // Calculate coverage
    CALCULATE_COVERAGE(MAP_READS.out.bam)

    // Combine coverage statistics
    COMBINE_COVERAGE_STATS(CALCULATE_COVERAGE.out.depth.map { it[1] }.collect())

    emit:
    bam = MAP_READS.out.bam
    depth = CALCULATE_COVERAGE.out.depth
    stats = COMBINE_COVERAGE_STATS.out.combined_stats
}
