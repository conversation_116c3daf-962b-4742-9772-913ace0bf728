// RdRP detection module for Nextflow pipeline

process RDRP_DETECTION {
    tag "${meta.id}"
    publishDir "${params.outdir}/04viral_detection/${meta.id}/rdrp", mode: 'copy'

    container 'containers/genomad-debian.sif'

    cpus 4
    memory 8.GB

    errorStrategy { task.exitStatus in [143,137,104,134,139] ? 'retry' : 'terminate' }
    maxRetries 3

    input:
    tuple val(meta), path(proteins_faa)

    output:
    tuple val(meta), path("*_rdrp_hits.txt"), emit: rdrp_hits
    tuple val(meta), path("*_rdrp_hits.faa"), optional: true, emit: rdrp_proteins
    tuple val(meta), path("*_rdrp_contigs.txt"), emit: rdrp_contigs
    path "versions.yml", emit: versions

    script:
    def args = task.ext.args ?: ''
    def prefix = task.ext.prefix ?: "${meta.id}"

    """
    # Check if the database exists and is accessible
    echo "Checking RdRP model at /databases/rdrp_model"
    if [ ! -f "/databases/rdrp_model" ]; then
        echo "ERROR: RdRP model file not found at /databases/rdrp_model"
        ls -la /databases/ || echo "Cannot access /databases directory"
        echo "Database binding may have failed. Check container binding options."
        exit 1
    else
        echo "RdRP model file found at /databases/rdrp_model"
        ls -la /databases/rdrp_model
    fi

    # Check if the input file exists and has content
    echo "Checking input file: ${proteins_faa}"
    if [ ! -s "${proteins_faa}" ]; then
        echo "WARNING: Input file ${proteins_faa} is empty or does not exist"
        ls -la ${proteins_faa} || echo "Cannot access input file"

        # Create empty output files
        echo "Creating empty output files since input is empty"
        touch ${prefix}_rdrp_hits.txt
        touch ${prefix}_rdrp_hits.faa
        touch ${prefix}_rdrp_contigs.txt
    else
        echo "Input file exists and has content"
        grep -c ">" ${proteins_faa} || echo "No FASTA headers found in input file"

        # Run hmmsearch to find RdRP domains with detailed logging
        echo "Running hmmsearch to find RdRP domains"
        set +e
        hmmsearch \\
            --cpu ${task.cpus} \\
            -E 1e-5 \\
            --tblout ${prefix}_rdrp_hits.txt \\
            /databases/rdrp_model \\
            ${proteins_faa}
        hmmsearch_exit_code=\$?
        set -e

        echo "hmmsearch exit code: \$hmmsearch_exit_code"

        if [ \$hmmsearch_exit_code -ne 0 ]; then
            echo "hmmsearch failed with exit code \$hmmsearch_exit_code"
            echo "Creating empty output files due to hmmsearch failure"
            touch ${prefix}_rdrp_hits.txt
            touch ${prefix}_rdrp_hits.faa
            touch ${prefix}_rdrp_contigs.txt
            exit 0
        fi

        # Extract sequences of proteins with RdRP hits
        if [ -s "${prefix}_rdrp_hits.txt" ]; then
            # Get protein IDs with RdRP hits (skip header lines starting with #)
            grep -v "^#" ${prefix}_rdrp_hits.txt | awk '{print \$1}' > rdrp_protein_ids.txt

            # Create a file with contig IDs that have RdRP hits (for viral screening summary)
            if [ -s "rdrp_protein_ids.txt" ]; then
                # Extract contig IDs from protein IDs
                cat rdrp_protein_ids.txt | while read protein_id; do
                    # Extract contig ID from protein ID (assuming format like NODE_123_length_456_cov_7.89_1)
                    # Remove the last _n suffix and anything after whitespace
                    contig_id=\$(echo "\$protein_id" | sed 's/_[0-9]*\$//' | cut -d ' ' -f1)
                    echo "\$contig_id" >> ${prefix}_rdrp_contigs.txt
                done
                # Remove duplicates
                sort -u ${prefix}_rdrp_contigs.txt > ${prefix}_rdrp_contigs.txt.tmp
                mv ${prefix}_rdrp_contigs.txt.tmp ${prefix}_rdrp_contigs.txt
            else
                # Create empty contigs file if no protein IDs
                touch ${prefix}_rdrp_contigs.txt
            fi

            # Extract sequences
            if [ -s "rdrp_protein_ids.txt" ]; then
                # Initialize output file
                touch ${prefix}_rdrp_hits.faa

                # Process each protein ID
                while read protein_id; do
                    # Extract the sequence and the following lines until the next header
                    awk -v target="\$protein_id" 'BEGIN {print_flag=0}
                    /^>/ {
                        if (index(\$0, target) > 0) {
                            print_flag=1
                            print \$0
                        } else {
                            print_flag=0
                        }
                    }
                    !/^>/ {
                        if (print_flag==1) {
                            print \$0
                        }
                    }' ${proteins_faa} >> ${prefix}_rdrp_hits.faa
                done < rdrp_protein_ids.txt
            else
                # Create empty hits file if no protein IDs
                touch ${prefix}_rdrp_hits.faa
            fi
        else
            # Create empty output files if no hits
            touch ${prefix}_rdrp_hits.faa
            touch ${prefix}_rdrp_contigs.txt
        fi
    fi

    cat <<-END_VERSIONS > versions.yml
    "${task.process}":
        hmmer: \$(hmmsearch -h | grep "HMMER" | sed 's/.*HMMER //; s/ .*//')
    END_VERSIONS
    """
}
