// Pathogen mapping module for Nextflow pipeline

process PATHOGEN_MAPPING {
    tag "${meta.id}"
    publishDir "${params.outdir}/02mapping/${meta.id}", mode: 'copy'

    container 'mapping-debian.sif'

    cpus 4
    memory 8.GB

    input:
    tuple val(meta), path(reads)

    output:
    tuple val(meta), path("bams/${meta.id}___pathdb.sorted.bam"), emit: bam
    tuple val(meta), path("bams/${meta.id}___pathdb.sorted.bam.bai"), emit: bai
    tuple val(meta), path("depth/${meta.id}_depth.tab"), optional: true, emit: depth
    tuple val(meta), path("ref/pathogen_db.fna"), emit: ref
    tuple val(meta), path("ref/indexed"), emit: index
    path "versions.yml", emit: versions

    script:
    def args = task.ext.args ?: ''
    def prefix = task.ext.prefix ?: "${meta.id}"
    def reads_args
    if (meta.single_end) {
        reads_args = "-U ${reads}"
    } else if (reads instanceof List) {
        reads_args = "-1 ${reads[0]} -2 ${reads[1]}"
    } else {
        // Interleaved reads
        reads_args = "-p ${reads}"
    }

    """
    # Create output directories
    mkdir -p bams depth ref/indexed

    # Copy the pathogen database to a writable location
    cp /databases/pathogen_db ref/pathogen_db.fna

    # Index pathogen database if not already indexed
    if [ ! -f "ref/indexed/pathdb.bwt.2bit.64" ]; then
        echo "Indexing pathogen database..."
        bwa-mem2 index -p ref/indexed/pathdb ref/pathogen_db.fna
    fi

    # Map reads to pathogen database
    echo "Mapping reads to pathogen database..."
    bwa-mem2 mem \\
        -t ${task.cpus} \\
        ${args} \\
        ref/indexed/pathdb \\
        ${reads_args} | \\
        samtools view -q 10 -bS - | \\
        samtools sort -o bams/${prefix}___pathdb.sorted.bam -

    # Index BAM file
    echo "Indexing BAM file..."
    samtools index bams/${prefix}___pathdb.sorted.bam

    # Calculate coverage statistics
    echo "Calculating coverage statistics..."
    python3 /usr/local/bin/get_depth_coverage_single.py \\
        --bam bams/${prefix}___pathdb.sorted.bam \\
        --assembly ref/pathogen_db.fna \\
        --sample-name ${prefix} \\
        --mapq 10 \\
        --paired n \\
        --out depth/${prefix}_depth.tab

    cat <<-END_VERSIONS > versions.yml
    "${task.process}":
        bwa-mem2: \$(bwa-mem2 version 2>&1 | grep -o 'Version: [0-9.]*' | sed 's/Version: //')
        samtools: \$(samtools --version | head -n1 | sed 's/^.*samtools //')
    END_VERSIONS
    """
}

// Process to generate a summary of pathogen detection results
process PATHOGEN_DETECTION_SUMMARY {
    tag "${meta.id}"
    publishDir "${params.outdir}/02mapping/${meta.id}", mode: 'copy'

    container 'mapping-debian.sif'

    cpus 1
    memory 2.GB

    input:
    tuple val(meta), path(self_bam), path(self_bai)
    tuple val(meta), path(pathogen_bam), path(pathogen_bai)

    output:
    tuple val(meta), path("stats/mapping_stats_self.tsv"), emit: self_stats
    tuple val(meta), path("stats/mapping_stats_pathdb.tsv"), emit: pathogen_stats
    path "versions.yml", emit: versions

    script:
    def prefix = task.ext.prefix ?: "${meta.id}"

    """
    # Create output directory
    mkdir -p stats

    # Generate mapping statistics
    generate_mapping_stats.sh \\
        ${self_bam} \\
        ${pathogen_bam} \\
        stats \\
        ${prefix}

    cat <<-END_VERSIONS > versions.yml
    "${task.process}":
        bash: \$(bash --version | head -n 1 | sed 's/.*version //; s/ .*//')
    END_VERSIONS
    """
}

// Workflow for pathogen detection
workflow PATHOGEN_DETECTION_WORKFLOW {
    take:
    reads_ch // Channel with [meta, reads]
    self_bam_ch // Channel with [meta, self_bam, self_bai]

    main:
    // Map reads to pathogen database
    PATHOGEN_MAPPING(reads_ch)

    // Generate a summary of pathogen detection results
    PATHOGEN_DETECTION_SUMMARY(
        self_bam_ch,
        PATHOGEN_MAPPING.out.bam.join(PATHOGEN_MAPPING.out.bai)
    )

    emit:
    pathogen_bam = PATHOGEN_MAPPING.out.bam
    pathogen_bai = PATHOGEN_MAPPING.out.bai
    self_stats = PATHOGEN_DETECTION_SUMMARY.out.self_stats
    pathogen_stats = PATHOGEN_DETECTION_SUMMARY.out.pathogen_stats
}
