#!/usr/bin/env nextflow

// Assembly module for microbiome analysis pipeline
// Handles metagenomic assembly with SPAdes (meta mode for DNA, rnaviral mode for RNA)

process ASSEMBLY {
    tag "${meta.id}"
    publishDir "${params.outdir}/01assembly/spades/${meta.id}", mode: 'copy', pattern: "contigs.fasta"

    container 'quay.io/biocontainers/spades:3.15.5--h95f258a_1'

    cpus 4
    memory 8.GB
    time { 4.hour * task.attempt }

    errorStrategy { task.exitStatus in [137,140] ? 'retry' : 'finish' }
    maxRetries 3

    input:
    tuple val(meta), path(reads)

    output:
    tuple val(meta), path("contigs.fasta"), emit: raw_assembly

    script:
    def mode = meta.is_rna ? "--rnaviral" : "--meta"
    """
    # Run SPAdes assembly
    spades.py ${mode} --threads ${task.cpus} --memory ${task.memory.toMega()} -o assembly_dir --12 ${reads}

    # Copy contigs to output
    cp assembly_dir/contigs.fasta contigs.fasta
    """
}

process FILTER_CONTIGS {
    tag "${meta.id}"
    publishDir "${params.outdir}/01assembly/assemblies_fna", mode: 'copy'

    container 'assembly_pixi.sif'

    input:
    tuple val(meta), path(assembly)

    output:
    tuple val(meta), path("${meta.id}.fna"), emit: filtered_assembly

    script:
    def min_length = meta.is_rna ? params.rna_min_length : params.dna_min_length
    """
    # Filter contigs by length using awk
    awk -v min_len=${min_length} '/^>/ {if (seqlen>=min_len) print seq; seq=""; print \$0; seqlen=0; next} {seq = seq \$0; seqlen += length(\$0)} END {if (seqlen>=min_len) print seq}' ${assembly} > ${meta.id}.fna
    """
}

process GENERATE_ASSEMBLY_STATS {
    tag "${meta.id}"
    publishDir "${params.outdir}/stats", mode: 'copy'

    container 'assembly_pixi.sif'

    input:
    tuple val(meta), path(assembly)

    output:
    path "${meta.id}_assembly_stats.txt", emit: assembly_stats

    script:
    """
    # Count number of sequences
    NUM_SEQS=\$(grep -c "^>" ${assembly})

    # Calculate total length and N50
    awk '/^>/ {if (seqlen) {print seqlen}; seqlen=0; next} {seqlen += length(\$0)} END {if (seqlen) print seqlen}' ${assembly} | \
        sort -nr | \
        awk 'BEGIN {total=0} {total += \$1; print total, \$1}' | \
        awk -v TOT=\$(awk '/^>/ {if (seqlen) {print seqlen}; seqlen=0; next} {seqlen += length(\$0)} END {if (seqlen) print seqlen}' ${assembly} | paste -sd+ | bc) \
            'BEGIN {N50=0} {if (N50==0 && \$1 >= TOT/2) {N50=\$2}} END {print "N50:", N50}' > ${meta.id}_assembly_stats.txt

    # Calculate GC content
    awk '/^>/ {next} {seq = seq \$0} END {gsub(/[^GCgc]/, "", seq); print "GC%:", length(seq)/length(\$0)*100}' ${assembly} >> ${meta.id}_assembly_stats.txt

    # Add number of sequences
    echo "Sequences: \$NUM_SEQS" >> ${meta.id}_assembly_stats.txt

    # Calculate total length
    awk '/^>/ {if (seqlen) {print seqlen}; seqlen=0; next} {seqlen += length(\$0)} END {if (seqlen) print seqlen}' ${assembly} | \
        paste -sd+ | \
        bc | \
        awk '{print "Total length:", \$1}' >> ${meta.id}_assembly_stats.txt
    """
}

// Workflow to perform assembly
workflow ASSEMBLY_WORKFLOW {
    take:
    reads_ch // Channel with [meta, reads]

    main:
    // Run assembly
    ASSEMBLY(reads_ch)

    // Filter contigs by length
    FILTER_CONTIGS(ASSEMBLY.out.raw_assembly)

    // Generate assembly statistics
    GENERATE_ASSEMBLY_STATS(FILTER_CONTIGS.out.filtered_assembly)

    emit:
    assembly = FILTER_CONTIGS.out.filtered_assembly
    stats = GENERATE_ASSEMBLY_STATS.out.assembly_stats
}
