#!/usr/bin/env nextflow

// Read processing module for microbiome analysis pipeline
// Handles interleaving paired-end reads and optional quality filtering

process BBDUK_READS {
    tag "${meta.id}"
    publishDir "${params.outdir}/00data/reads", mode: 'copy', pattern: "${meta.id}.fastq.gz"
    publishDir "${params.outdir}/00data/readsf/${meta.id}", mode: 'copy', pattern: "${meta.id}.fastq.gz"
    publishDir "${params.outdir}/00data/readsf/${meta.id}", mode: 'copy', pattern: "bbduk_stats.txt"

    container 'quay.io/biocontainers/bbmap:38.97--h5c4e2a8_1'

    // BBDuk requires fewer resources than RQCFilter
    cpus 8
    memory '16 GB'
    time '1h'

    input:
    tuple val(meta), path(reads)

    output:
    tuple val(meta), path("${meta.id}.fastq.gz"), emit: interleaved_reads
    path "bbduk_stats.txt", emit: bbduk_stats

    script:
    if (meta.single_end) {
        // For single-end reads
        def read_file = reads
        """
        # Run BBDuk on single-end reads for basic quality filtering and adapter trimming
        bbduk.sh \\
            in=${read_file} \\
            out=${meta.id}.fastq.gz \\
            ref=adapters,phix \\
            ktrim=r \\
            k=23 \\
            mink=11 \\
            hdist=1 \\
            qtrim=r \\
            trimq=14 \\
            maq=10 \\
            minlen=50 \\
            stats=bbduk_stats.txt \\
            threads=${task.cpus}
        """
    } else {
        // For paired-end reads
        def read1 = reads[0]
        def read2 = reads[1]
        """
        # Run BBDuk on paired-end reads for basic quality filtering and adapter trimming
        bbduk.sh \\
            in1=${read1} \\
            in2=${read2} \\
            out=${meta.id}.fastq.gz \\
            ref=adapters,phix \\
            ktrim=r \\
            k=23 \\
            mink=11 \\
            hdist=1 \\
            qtrim=r \\
            trimq=14 \\
            maq=10 \\
            minlen=50 \\
            stats=bbduk_stats.txt \\
            interleaved=t \\
            threads=${task.cpus}
        """
    }
}

process FILTER_READS {
    tag "${meta.id}"
    publishDir "${params.outdir}/00data/readsf/${meta.id}", mode: 'copy'

    container 'quay.io/biocontainers/bbmap:38.97--h5c4e2a8_1'

    input:
    tuple val(meta), path(reads)

    output:
    tuple val(meta), path("${meta.id}.anqdpht.fastq.gz"), emit: filtered_reads

    script:
    """
    bbduk.sh in=${reads} out=${meta.id}.anqdpht.fastq.gz \
        qtrim=rl trimq=20 minlength=50 \
        ref=adapters,phix ktrim=r k=23 mink=11 hdist=1
    """
}

process GENERATE_READ_STATS {
    tag "${meta.id}"
    publishDir "${params.outdir}/stats", mode: 'copy'

    container 'quay.io/biocontainers/seqkit:2.3.1--h9ee0642_0'

    input:
    tuple val(meta), path(reads)

    output:
    path "${meta.id}_read_stats.txt", emit: read_stats

    script:
    """
    seqkit stats ${reads} > ${meta.id}_read_stats.txt
    """
}

// Workflow to process reads
workflow READ_PROCESSING {
    take:
    read_pairs_ch // Channel with [meta, [r1, r2]]

    main:
    // Run BBDuk on paired-end reads for quality filtering and adapter trimming
    BBDUK_READS(read_pairs_ch)

    // Generate read statistics
    GENERATE_READ_STATS(BBDUK_READS.out.interleaved_reads)

    // Optionally filter reads if params.filter_reads is true
    if (params.filter_reads) {
        FILTER_READS(BBDUK_READS.out.interleaved_reads)
        reads_for_assembly = FILTER_READS.out.filtered_reads
    } else {
        reads_for_assembly = BBDUK_READS.out.interleaved_reads
    }

    emit:
    reads = reads_for_assembly
    stats = GENERATE_READ_STATS.out.read_stats
    bbduk_stats = BBDUK_READS.out.bbduk_stats
}
