// CheckV module for Nextflow pipeline

process CHECKV_END_TO_END {
    tag "${meta.id}"
    publishDir "${params.outdir}/04viral_detection/${meta.id}/checkv", mode: 'copy'

    container 'containers/checkv-debian.sif'

    cpus 4
    memory 8.GB

    errorStrategy { task.exitStatus in [143,137,104,134,139] ? 'retry' : 'terminate' }
    maxRetries 3

    input:
    tuple val(meta), path(virus_fasta)

    output:
    tuple val(meta), path("*_checkv/quality_summary.tsv"), emit: quality_summary
    tuple val(meta), path("*_checkv/completeness.tsv"), emit: completeness
    tuple val(meta), path("*_checkv/contamination.tsv"), emit: contamination
    tuple val(meta), path("*_checkv/complete_genomes.tsv"), optional: true, emit: complete_genomes
    tuple val(meta), path("*_checkv/viruses.fna"), emit: viruses_fasta
    path "versions.yml", emit: versions

    script:
    def args = task.ext.args ?: ''
    def prefix = task.ext.prefix ?: "${meta.id}"

    """
    # Check if the database exists and is accessible
    echo "Checking CheckV database at /databases/checkv-db"
    if [ ! -d "/databases/checkv-db" ]; then
        echo "ERROR: CheckV database directory not found at /databases/checkv-db"
        ls -la /databases/ || echo "Cannot access /databases directory"
        echo "Database binding may have failed. Check container binding options."
        exit 1
    else
        echo "CheckV database directory found at /databases/checkv-db"
        ls -la /databases/checkv-db | head -n 10
    fi

    # Check if the input file exists and has content
    echo "Checking input file: ${virus_fasta}"
    if [ ! -s "${virus_fasta}" ]; then
        echo "WARNING: Input file ${virus_fasta} is empty or does not exist"
        ls -la ${virus_fasta} || echo "Cannot access input file"

        # Create empty output directory and files
        echo "Creating empty output files since input is empty"
        mkdir -p ${prefix}_checkv
        echo -e "contig_id\\tcontig_length\\tcheckv_quality\\tmiuvig_quality\\tcompleteness\\tcompleteness_method\\tcontamination\\tkmer_freq\\taai_completeness\\taai_confidence\\taai_error\\taai_top_hit\\taai_passing\\taai_genomes\\taai_msg\\tcheckv_msg" > ${prefix}_checkv/quality_summary.tsv
        echo -e "contig_id\\tcontig_length\\tcompleteness\\tcompleteness_method\\tcompleteness_msg" > ${prefix}_checkv/completeness.tsv
        echo -e "contig_id\\tcontig_length\\tcontamination\\tcontamination_method\\tcontamination_msg" > ${prefix}_checkv/contamination.tsv
        touch ${prefix}_checkv/viruses.fna
    else
        echo "Input file exists and has content"
        grep -c ">" ${virus_fasta} || echo "No FASTA headers found in input file"

        # Run CheckV end-to-end with detailed logging
        echo "Running CheckV end-to-end with database at /databases/checkv-db"
        set +e
        checkv end_to_end \\
            ${virus_fasta} \\
            ${prefix}_checkv \\
            -t ${task.cpus} \\
            -d /databases/checkv-db
        checkv_exit_code=\$?
        set -e

        echo "CheckV exit code: \$checkv_exit_code"

        if [ \$checkv_exit_code -ne 0 ]; then
            echo "CheckV failed with exit code \$checkv_exit_code"
            echo "Creating empty output files due to CheckV failure"
            mkdir -p ${prefix}_checkv
            echo -e "contig_id\\tcontig_length\\tcheckv_quality\\tmiuvig_quality\\tcompleteness\\tcompleteness_method\\tcontamination\\tkmer_freq\\taai_completeness\\taai_confidence\\taai_error\\taai_top_hit\\taai_passing\\taai_genomes\\taai_msg\\tcheckv_msg" > ${prefix}_checkv/quality_summary.tsv
            echo -e "contig_id\\tcontig_length\\tcompleteness\\tcompleteness_method\\tcompleteness_msg" > ${prefix}_checkv/completeness.tsv
            echo -e "contig_id\\tcontig_length\\tcontamination\\tcontamination_method\\tcontamination_msg" > ${prefix}_checkv/contamination.tsv
            touch ${prefix}_checkv/viruses.fna
        else
            echo "CheckV completed successfully"
            # Check if output files were created
            ls -la ${prefix}_checkv/ || echo "No CheckV output directory found"
        fi
    fi

    # If viruses.fna doesn't exist, create an empty one
    if [ ! -f "${prefix}_checkv/viruses.fna" ]; then
        echo "viruses.fna not found, creating empty file"
        touch ${prefix}_checkv/viruses.fna
    fi

    cat <<-END_VERSIONS > versions.yml
    "${task.process}":
        checkv: \$(checkv --version 2>&1 | sed 's/CheckV v//g')
    END_VERSIONS
    """
}
