#!/bin/bash

# This script fixes the CREATE_DATAFRAME process in the Nextflow workflow
# by correctly handling the paths to the processed hits and GTDB classification

# Set the results directory
RESULTS_DIR="../results_GCF_025823245"
GTDB_FILE="${RESULTS_DIR}/gtdbtk/gtdb_classification.csv"

echo "Starting CREATE_DATAFRAME fix script"
echo "Results directory: ${RESULTS_DIR}"
echo "GTDB classification file: ${GTDB_FILE}"

# Verify the GTDB classification file exists
if [ ! -f "${GTDB_FILE}" ]; then
    echo "ERROR: GTDB classification file not found at ${GTDB_FILE}"
    exit 1
fi

# Create directory structure expected by the script
echo "Creating directory structure for csv_hits..."
mkdir -p csv_hits
for i in $(seq 1 66); do
    mkdir -p csv_hits/batch_$i
done

# Copy files from results/processed_hits directly
echo "Copying CSV files from ${RESULTS_DIR}/processed_hits..."
for i in $(seq 1 66); do
    echo "Processing batch $i"
    # Check if the batch directory exists
    if [ -d "${RESULTS_DIR}/processed_hits/batch_$i" ]; then
        # Use find to locate all CSV files in the results directory for this batch
        find "${RESULTS_DIR}/processed_hits/batch_$i" -name "*.csv" -exec cp {} csv_hits/batch_$i/ \; 2>/dev/null || echo "No CSV files found for batch $i"
    else
        echo "Batch directory ${RESULTS_DIR}/processed_hits/batch_$i does not exist"
    fi
done

# Count the CSV files that were copied
CSV_COUNT=$(find csv_hits -type f -name "*.csv" | wc -l)
echo "Copied ${CSV_COUNT} CSV files to csv_hits directory"

# Run the script with the organized directory structure
echo "Running create_unique_df_hits_optimized.py..."
python ../create_unique_df_hits_optimized.py csv_hits "${GTDB_FILE}" final_results.csv

# Check if the script was successful
if [ $? -eq 0 ] && [ -f "final_results.csv" ]; then
    echo "Successfully created final_results.csv"
    # Copy the result to the output directory
    cp final_results.csv "${RESULTS_DIR}/"
    echo "Copied final_results.csv to ${RESULTS_DIR}/"
    echo "CREATE_DATAFRAME process completed successfully!"
    exit 0
else
    echo "ERROR: Failed to create final_results.csv"
    exit 1
fi
