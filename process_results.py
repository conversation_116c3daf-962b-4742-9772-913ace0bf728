#!/usr/bin/env python3
# Script to process existing CSV files in the results/processed_hits directory

import os
import sys
import shutil
import subprocess

def main():
    # Define paths
    results_dir = "results"
    processed_hits_dir = os.path.join(results_dir, "processed_hits")
    gtdb_classification = os.path.join(results_dir, "gtdbtk", "gtdb_classification.csv")
    output_file = os.path.join(results_dir, "final_results.csv")
    
    # Create a temporary directory structure for the script
    temp_dir = "temp_csv_hits"
    if os.path.exists(temp_dir):
        shutil.rmtree(temp_dir)
    os.makedirs(temp_dir)
    
    # Find all batch directories
    batch_dirs = []
    for item in os.listdir(processed_hits_dir):
        if item.startswith("batch_"):
            batch_dir = os.path.join(processed_hits_dir, item)
            if os.path.isdir(batch_dir):
                batch_dirs.append(item)
    
    print(f"Found {len(batch_dirs)} batch directories: {batch_dirs}")
    
    # Create corresponding directories in the temp directory
    for batch_dir in batch_dirs:
        os.makedirs(os.path.join(temp_dir, batch_dir))
        
        # Copy all CSV files from the batch directory to the temp directory
        source_dir = os.path.join(processed_hits_dir, batch_dir)
        dest_dir = os.path.join(temp_dir, batch_dir)
        
        csv_files = [f for f in os.listdir(source_dir) if f.endswith('.csv')]
        print(f"Copying {len(csv_files)} CSV files from {source_dir} to {dest_dir}")
        
        for csv_file in csv_files:
            shutil.copy2(os.path.join(source_dir, csv_file), os.path.join(dest_dir, csv_file))
    
    # Run the create_unique_df_hits_optimized.py script
    script_path = "bin/create_unique_df_hits_optimized.py"
    
    print(f"Running: python {script_path} {temp_dir} {gtdb_classification} {output_file}")
    
    try:
        result = subprocess.run(
            ["python", script_path, temp_dir, gtdb_classification, output_file],
            check=True,
            text=True,
            capture_output=True
        )
        print("Script output:")
        print(result.stdout)
        
        if result.stderr:
            print("Script errors:")
            print(result.stderr)
            
        print(f"Script completed successfully. Output saved to {output_file}")
    except subprocess.CalledProcessError as e:
        print(f"Error running script: {e}")
        print("Script output:")
        print(e.stdout)
        print("Script errors:")
        print(e.stderr)
        return 1
    
    # Clean up
    print(f"Cleaning up temporary directory {temp_dir}")
    shutil.rmtree(temp_dir)
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
