Bootstrap: docker
From: ubuntu:20.04

%labels
    MAIN<PERSON><PERSON>ER Lorenzo
    DESCRIPTION Singularity container for HMMER with Python

%environment
    export LC_ALL=C
    export PATH="/usr/local/bin:$PATH"
    export PYTHONPATH="/usr/local/lib/python3.8/site-packages:$PYTHONPATH"

%post
    # Install system dependencies
    apt-get update && apt-get install -y --no-install-recommends \
        build-essential \
        ca-certificates \
        git \
        wget \
        python3 \
        python3-pip \
        python3-dev \
        python3-setuptools \
        python3-wheel \
        hmmer \
        && rm -rf /var/lib/apt/lists/*

    # Create symbolic links for Python
    ln -sf /usr/bin/python3 /usr/bin/python
    ln -sf /usr/bin/pip3 /usr/bin/pip

    # Install Python packages
    pip install --no-cache-dir pandas numpy biopython

    # Verify installation
    hmmsearch -h

%runscript
    exec "$@"
