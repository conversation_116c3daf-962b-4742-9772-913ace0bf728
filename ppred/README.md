# ppred: Pathogenic Potential Prediction Workflow

A Nextflow-based bioinformatics pipeline for predicting the pathogenic potential of bacterial genomes from Metagenome-Assembled Genomes (MAGs).

## Overview

This workflow processes genomic data through a series of steps:
1. **Prodigal** for protein annotation
2. **GTDB-Tk** for taxonomic classification
3. **HMMER** for protein family analysis
4. **Processing of HMMER hits**
5. **Creation of a unified dataframe** with taxonomic information
6. **Feature encoding** using an autoencoder
7. **Pathogenicity prediction** using a trained machine learning model

## Workflow Schematic

```
  Input Genome (FNA)
       │
       ▼
  ┌─────────────┐
  │             │
  │  PRODIGAL   │ ──► Protein annotations (FAA)
  │             │
  └──────┬──────┘
         │
         ▼
  ┌─────────────┐
  │             │
  │   GTDB-Tk   │ ──► Taxonomic classification (CSV)
  │             │
  └──────┬──────┘
         │
         ▼
  ┌─────────────┐
  │             │
  │  HMMSEARCH  │ ──► HMMER search results (CSV)
  │ (all batches)│     (processed in parallel)
  │             │
  └──────┬──────┘
         │
         ▼
  ┌─────────────┐
  │             │
  │ PROCESS HITS│ ──► Processed hits (CSV)
  │ (all batches)│     (processed in parallel)
  │             │
  └──────┬──────┘
         │
         ▼
  ┌─────────────┐
  │             │
  │    CREATE   │
  │  DATAFRAME  │ ──► Combined results (CSV)
  │             │
  └──────┬──────┘
         │
         ▼
  ┌─────────────┐
  │             │
  │ AUTOENCODER │ ──► Encoded features (CSV)
  │             │
  └──────┬──────┘
         │
         ▼
  ┌─────────────┐
  │             │
  │    MODEL    │ ──► Prediction results (CSV)
  │   PREDICT   │
  │             │
  └─────────────┘
```

## Requirements

- Nextflow (>=20.10.0)
- Singularity (>=3.5.0)
- Python 3.8+

## Installation

1. Clone this repository:
   ```
   git clone https://github.com/NeLLi-team/ppred.git
   cd ppred
   ```

2. Build the Singularity containers:
   ```
   bash build_containers.sh
   ```
   This will create the following containers in the `containers` directory:
   - `prodigal.sif`: Prodigal with Python
   - `gtdbtk.sif`: GTDB-Tk with Python
   - `hmmer.sif`: HMMER with Python

## Directory Structure

The workflow expects the following directory structure for the HMM profiles:

```
hmm_base_dir/
├── batch_1/
│   ├── hmm_file1.hmm
│   ├── hmm_file2.hmm
│   └── ...
├── batch_2/
│   ├── hmm_file1.hmm
│   ├── hmm_file2.hmm
│   └── ...
├── batch_3/
│   └── ...
...
└── batch_66/
    └── ...
```

By default, the workflow processes 66 batch directories, but this can be adjusted using the `batch_count` parameter.

## Usage

The genome parameter is required to run the workflow:
```
nextflow run main.nf --genome /path/to/genome.fna
```

Run with additional custom parameters:
```
nextflow run main.nf --genome /path/to/genome.fna --outdir /path/to/results --batch_count 66
```

The workflow will automatically check if intermediate results already exist and skip those steps, making it efficient for re-runs.

### Parameters

| Parameter | Description | Default |
|-----------|-------------|---------|
| `genome` | Input genome file (FNA format) | `null` (must be provided by user) |
| `outdir` | Output directory | `$baseDir/results` |
| `gtdbtk_db` | Path to GTDB-Tk database | `/clusterfs/jgi/scratch/science/mgs/nelli/databases/gtdbtk/release220` |
| `hmm_base_dir` | Base directory containing HMM batch directories | `/clusterfs/jgi/scratch/science/mgs/nelli/lorenzo/1_pangenomes_features/genera_pangenome_est_30_Sept/hmm_est_5_30_Sept` |
| `batch_count` | Number of batch directories to process | `66` |
| `scaler_path` | Path to the saved scaler | `$baseDir/robustscaler_enc1024_layers1.pkl` |
| `model_path` | Path to the saved autoencoder model | `$baseDir/robustscaler_enc1024_layers1.h5` |
| `autoencoder_output` | Output file for autoencoder results | `encoded_features.csv` |
| `ml_model_path` | Path to the ML model for prediction | `$baseDir/best_refined_model_RS.pkl` |
| `prediction_output` | Output file for prediction results | `prediction_results.csv` |
| `predict_version` | Version parameter for the MODEL_PREDICT process | `1` |

## Output

The workflow generates the following outputs:
- Protein annotations (FAA files)
- Taxonomic classifications (CSV files)
- HMMER search results (CSV files)
- Processed hits (CSV files)
- Final dataframe with taxonomic information and HMM hits (CSV file)
- Encoded features from the autoencoder (CSV file)
- Prediction results (CSV file)

## License

[MIT License](LICENSE)

## Citation

If you use this workflow in your research, please cite:
[Citation information to be added]
